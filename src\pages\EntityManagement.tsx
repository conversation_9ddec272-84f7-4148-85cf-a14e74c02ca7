import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { useParams, useNavigate, useSearchParams } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Plus, 
  Search, 
  Filter, 
  MoreHorizontal, 
  Settings, 
  Trash2, 
  RefreshCw, 
  Wifi, 
  WifiOff, 
  AlertCircle,
  Building2,
  Database,
  Activity,
  Eye,
  Link,
  Unlink
} from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { CreateEntityModal } from '@/components/entities/CreateEntityModal';
import { EntityDashboard } from '@/components/entities/EntityDashboard';
import { OAuthCallback, useOAuthCallback } from '@/components/entities/OAuthCallback';
import { EntitySettingsManagement } from '@/components/entities/EntitySettingsManagement';
import { EntitiesService } from '@/services/entities.service';
import { useClientStore } from '@/store/client.store';
import { AppSidebar } from '@/components/layout/AppSidebar';
import { useFirmName } from '@/hooks/useFirmName';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { Separator } from '@/components/ui/separator';
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from '@/components/ui/sidebar';
import type { 
  EntitySummary, 
  EntityListResponse, 
  EntityFilters,
  ConnectionStatus,
  EntityType,
  EntityStatus
} from '@/types/entity.types';

export function EntityManagement() {
  const { clientId, entityId } = useParams();
  const navigate = useNavigate();
  const { isOAuthCallback } = useOAuthCallback();
  const { firmName, isLoading: firmNameLoading } = useFirmName();
  
  // State
  const [entities, setEntities] = useState<EntitySummary[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedEntity, setSelectedEntity] = useState<EntitySummary | null>(null);
  const [activeTab, setActiveTab] = useState('list');
  
  // Modal states
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showSettingsModal, setShowSettingsModal] = useState(false);
  
  // Filters - simplified to prevent infinite loops
  const [filters, setFilters] = useState<EntityFilters>({
    page: 1,
    limit: 20,
    search: '',
    sort_by: 'name',
    sort_order: 'asc'
  });

  // Debounce search to prevent excessive API calls
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Client store
  const { currentClient, fetchClient } = useClientStore();

  // Load client details when clientId changes
  useEffect(() => {
    if (clientId && (!currentClient || currentClient.client_id !== clientId)) {
      fetchClient(clientId);
    }
  }, [clientId, currentClient, fetchClient]);

  const loadEntities = useCallback(async (currentFilters: EntityFilters) => {
    if (!clientId) return;
    
    try {
      setIsLoading(true);
      setError(null);
      const response = await EntitiesService.getEntitiesForClient(clientId, currentFilters);
      console.log('DEBUG: Entities from backend:', response.entities);
      setEntities(response.entities);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load entities');
    } finally {
      setIsLoading(false);
    }
  }, [clientId]);

  // Load entities when filters change (with debouncing for search)
  useEffect(() => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // If search is being typed, debounce it
    if (filters.search !== '') {
      searchTimeoutRef.current = setTimeout(() => {
        loadEntities(filters);
      }, 300);
    } else {
      // For non-search filters, load immediately
      loadEntities(filters);
    }

    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, [filters, loadEntities]);

  useEffect(() => {
    // Handle entity selection from URL
    if (entityId && entities.length > 0) {
      const entity = entities.find(e => e.entity_id === entityId);
      if (entity) {
        setSelectedEntity(entity);
        setActiveTab('dashboard');
      }
    }
  }, [entityId, entities]);

  // Handle OAuth callback
  if (isOAuthCallback) {
    return (
      <OAuthCallback
        entityId={entityId}
        onSuccess={(entityId, response) => {
          navigate(`/clients/${clientId}/entities/${entityId}`);
        }}
        onError={(error) => {
          setError(error);
          navigate(`/clients/${clientId}/entities`);
        }}
        onCancel={() => {
          navigate(`/clients/${clientId}/entities`);
        }}
      />
    );
  }

  const handleCreateEntity = () => {
    setShowCreateModal(true);
  };

  const handleEntityCreated = (entityId: string) => {
    loadEntities(filters);
    navigate(`/clients/${clientId}/entities/${entityId}`);
  };

  const handleViewEntity = (entity: EntitySummary) => {
    setSelectedEntity(entity);
    setActiveTab('dashboard');
    navigate(`/clients/${clientId}/entities/${entity.entity_id}`);
  };

  const handleEditEntity = (entity: EntitySummary) => {
    console.log('handleEditEntity called with entity:', entity);
    setSelectedEntity(entity);
    setShowSettingsModal(true);
    console.log('Modal state set to true, selectedEntity:', entity.entity_name);
  };

  const handleDeleteEntity = async (entity: EntitySummary) => {
    if (!confirm(`Are you sure you want to delete "${entity.entity_name}"?`)) {
      return;
    }

    try {
      await EntitiesService.deleteEntity(entity.entity_id);
      loadEntities(filters);
      if (selectedEntity?.entity_id === entity.entity_id) {
        setSelectedEntity(null);
        setActiveTab('list');
        navigate(`/clients/${clientId}/entities`);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete entity');
    }
  };

  const handleSyncEntity = async (entity: EntitySummary) => {
    try {
      await EntitiesService.triggerSync(entity.entity_id);
      loadEntities(filters);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to trigger sync');
    }
  };

  const handleDisconnectEntity = async (entity: EntitySummary) => {
    if (!confirm(`Are you sure you want to disconnect "${entity.entity_name}"?`)) {
      return;
    }

    try {
      await EntitiesService.disconnectEntity(entity.entity_id);
      loadEntities(filters);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to disconnect entity');
    }
  };

  const getConnectionStatusBadge = (status?: ConnectionStatus) => {
    const variants: Record<ConnectionStatus | 'unknown', { variant: any; icon: React.ReactNode }> = {
      connected: { variant: 'default', icon: <Wifi className="h-3 w-3" /> },
      disconnected: { variant: 'secondary', icon: <WifiOff className="h-3 w-3" /> },
      error: { variant: 'destructive', icon: <AlertCircle className="h-3 w-3" /> },
      pending: { variant: 'outline', icon: <RefreshCw className="h-3 w-3" /> },
      expired: { variant: 'destructive', icon: <AlertCircle className="h-3 w-3" /> },
      syncing: { variant: 'default', icon: <RefreshCw className="h-3 w-3 animate-spin" /> },
      unknown: { variant: 'secondary', icon: <AlertCircle className="h-3 w-3" /> }
    };

    const currentStatus = status || 'unknown';
    const config = variants[currentStatus];
    
    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        {config.icon}
        {currentStatus.charAt(0).toUpperCase() + currentStatus.slice(1)}
      </Badge>
    );
  };

  const renderEntityList = () => (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Entity Management</h2>
          <p className="text-gray-500">
            Manage accounting software connections for {currentClient?.name || 'this client'}
          </p>
        </div>
        <Button onClick={handleCreateEntity}>
          <Plus className="h-4 w-4 mr-2" />
          Add Entity
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex gap-4 items-center">
            <div className="flex-1">
              <div className="relative">
                <Search className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <Input
                  placeholder="Search entities..."
                  value={filters.search || ''}
                  onChange={(e) => setFilters({ ...filters, search: e.target.value, page: 1 })}
                  className="pl-10"
                />
              </div>
            </div>
            <Select
              value={filters.type || 'all'}
              onValueChange={(value) => setFilters({ ...filters, type: value === 'all' ? undefined : value as EntityType, page: 1 })}
            >
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="xero">Xero</SelectItem>
                <SelectItem value="qbo">QuickBooks</SelectItem>
                <SelectItem value="manual">Manual</SelectItem>
              </SelectContent>
            </Select>
            <Select
              value={filters.connection_status || 'all'}
              onValueChange={(value) => setFilters({ ...filters, connection_status: value === 'all' ? undefined : value as ConnectionStatus, page: 1 })}
            >
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="connected">Connected</SelectItem>
                <SelectItem value="disconnected">Disconnected</SelectItem>
                <SelectItem value="error">Error</SelectItem>
                <SelectItem value="syncing">Syncing</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Entity List */}
      <div className="grid gap-4">
        {isLoading ? (
          [...Array(3)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
                  <div className="h-6 bg-gray-200 rounded w-1/2 mb-4"></div>
                  <div className="flex gap-2">
                    <div className="h-6 bg-gray-200 rounded w-20"></div>
                    <div className="h-6 bg-gray-200 rounded w-24"></div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        ) : entities.length === 0 ? (
          <Card>
            <CardContent className="p-12 text-center">
              <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No entities found</h3>
              <p className="text-gray-500 mb-4">
                {filters.search ? 'No entities match your search criteria.' : 'Get started by adding your first entity.'}
              </p>
              <Button onClick={handleCreateEntity}>
                <Plus className="h-4 w-4 mr-2" />
                Add Entity
              </Button>
            </CardContent>
          </Card>
        ) : (
          entities.map((entity) => (
            <Card key={entity.entity_id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="text-lg font-semibold">{entity.entity_name}</h3>
                      <Badge variant="outline">{entity.type ? entity.type.toUpperCase() : 'UNKNOWN TYPE'}</Badge>
                      {getConnectionStatusBadge(entity.connection_status)}
                    </div>
                    <div className="flex items-center gap-4 text-sm text-gray-500">
                      {entity.last_sync && (
                        <span>Last sync: {new Date(entity.last_sync).toLocaleString()}</span>
                      )}
                      {entity.health_score !== undefined && (
                        <span>Health: {entity.health_score}%</span>
                      )}
                      {entity.pending_items_count !== undefined && entity.pending_items_count > 0 && (
                        <span className="text-yellow-600">{entity.pending_items_count} pending</span>
                      )}
                      {entity.error_count !== undefined && entity.error_count > 0 && (
                        <span className="text-red-600">{entity.error_count} errors</span>
                      )}
                    </div>
                    {entity.error_message && (
                      <p className="text-sm text-red-600 mt-2">{entity.error_message}</p>
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleViewEntity(entity)}
                    >
                      <Eye className="h-4 w-4 mr-2" />
                      View
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEditEntity(entity)}
                    >
                      <Settings className="h-4 w-4 mr-2" />
                      Settings
                    </Button>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="outline" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleSyncEntity(entity)}>
                          <RefreshCw className="h-4 w-4 mr-2" />
                          Sync Now
                        </DropdownMenuItem>
                        {entity.connection_status === 'connected' && (
                          <DropdownMenuItem onClick={() => handleDisconnectEntity(entity)}>
                            <Unlink className="h-4 w-4 mr-2" />
                            Disconnect
                          </DropdownMenuItem>
                        )}
                        <DropdownMenuItem 
                          onClick={() => handleDeleteEntity(entity)}
                          className="text-red-600"
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );

  const renderEntityDashboard = () => {
    if (!selectedEntity) {
      return (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>No entity selected</AlertDescription>
        </Alert>
      );
    }

    return (
      <EntityDashboard
        entityId={selectedEntity.entity_id}
        onSettingsClick={() => setShowSettingsModal(true)}
        onSyncClick={() => loadEntities(filters)}
      />
    );
  };

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset className="flex-1 overflow-hidden">
        <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4">
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="mr-2 h-4" />
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem className="hidden md:block">
                <BreadcrumbLink
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    navigate('/dashboard');
                  }}
                  className="cursor-pointer"
                >
                  {firmNameLoading ? 'Loading...' : firmName}
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator className="hidden md:block" />
              <BreadcrumbItem>
                <BreadcrumbPage>
                  {currentClient?.name ? `${currentClient.name} - Entities` : 'Entity Management'}
                </BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </header>
        <div className="flex-1 overflow-auto">
          <div className="p-6">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="mb-6">
                <TabsTrigger value="list" className="flex items-center gap-2">
                  <Database className="h-4 w-4" />
                  Entities
                </TabsTrigger>
                {selectedEntity && (
                  <TabsTrigger value="dashboard" className="flex items-center gap-2">
                    <Activity className="h-4 w-4" />
                    Dashboard
                  </TabsTrigger>
                )}
              </TabsList>

              <TabsContent value="list">
                {renderEntityList()}
              </TabsContent>

              <TabsContent value="dashboard">
                {renderEntityDashboard()}
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </SidebarInset>

      {/* Create Entity Modal */}
      <CreateEntityModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        clientId={clientId || ''}
        clientName={currentClient?.name}
        onSuccess={handleEntityCreated}
      />

      {/* Entity Settings Modal */}
      {selectedEntity && showSettingsModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold">Entity Settings - {selectedEntity.entity_name}</h2>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowSettingsModal(false)}
              >
                ✕
              </Button>
            </div>
            <EntitySettingsManagement
              entityId={selectedEntity.entity_id}
              fetchSettings={async (entityId) => {
                const entity = await EntitiesService.getEntity(entityId);
                return {
                  entityId: entity.entity_id,
                  entityName: entity.entity_name,
                  prepaymentAssetAccountCodes: entity.settings?.prepayment_asset_account_codes || [],
                  defaultExpenseAccountCode: entity.settings?.default_expense_account_code || null,
                  defaultAmortizationMonths: entity.settings?.default_amortization_months || 12,
                  amortizationMaterialityThreshold: entity.settings?.amortization_materiality_threshold || 1000,
                  autoSyncEnabled: entity.settings?.auto_sync_enabled ?? true,
                  syncFrequency: entity.settings?.sync_frequency || 'daily',
                  syncSpendMoney: entity.settings?.sync_spend_money ?? true,
                  excludedPnlAccountCodes: entity.settings?.excluded_pnl_account_codes || [],
                  transactionSyncStartDate: entity.settings?.transaction_sync_start_date || '',
                  syncInvoices: entity.settings?.sync_invoices ?? true,
                  syncBills: entity.settings?.sync_bills ?? true,
                  syncPayments: entity.settings?.sync_payments ?? true,
                  syncBankTransactions: entity.settings?.sync_bank_transactions ?? true,
                  syncJournalEntries: entity.settings?.sync_journal_entries ?? true,
                  autoPostProposedJournals: entity.settings?.auto_post_proposed_journals ?? false,
                  baseCurrencyCode: entity.settings?.base_currency_code || 'USD'
                };
              }}
              fetchChartOfAccounts={async (entityId) => {
                const accounts = await EntitiesService.getChartOfAccounts(entityId);
                return accounts.map(account => ({
                  code: account.code,
                  name: account.name,
                  type: account.type as 'ASSET' | 'EXPENSE' | 'LIABILITY' | 'EQUITY' | 'REVENUE'
                }));
              }}
              saveSettings={async (entityId, settings) => {
                await EntitiesService.updateEntitySettings(entityId, {
                  prepayment_asset_account_codes: settings.prepaymentAssetAccountCodes,
                  default_expense_account_code: settings.defaultExpenseAccountCode,
                  default_amortization_months: settings.defaultAmortizationMonths,
                  amortization_materiality_threshold: settings.amortizationMaterialityThreshold,
                  auto_sync_enabled: settings.autoSyncEnabled,
                  sync_frequency: settings.syncFrequency,
                  sync_spend_money: settings.syncSpendMoney,
                  excluded_pnl_account_codes: settings.excludedPnlAccountCodes,
                  transaction_sync_start_date: settings.transactionSyncStartDate,
                  sync_invoices: settings.syncInvoices,
                  sync_bills: settings.syncBills,
                  sync_payments: settings.syncPayments,
                  sync_bank_transactions: settings.syncBankTransactions,
                  sync_journal_entries: settings.syncJournalEntries,
                  auto_post_proposed_journals: settings.autoPostProposedJournals,
                  base_currency_code: settings.baseCurrencyCode
                });
              }}
              onClose={() => setShowSettingsModal(false)}
              hideFooter={true}
              onSaveAction={(saveHandler, canSave, isSaving) => {
                // Custom save button in modal footer
              }}
            />
            <div className="flex justify-end gap-2 mt-6 pt-4 border-t">
              <Button
                variant="outline"
                onClick={() => setShowSettingsModal(false)}
              >
                Cancel
              </Button>
              <Button
                onClick={async () => {
                  try {
                    // The EntitySettingsManagement component handles its own save logic
                    // We just need to close the modal after successful save
                    setShowSettingsModal(false);
                    loadEntities(filters);
                  } catch (err) {
                    setError(err instanceof Error ? err.message : 'Failed to save settings');
                  }
                }}
              >
                Close
              </Button>
            </div>
          </div>
        </div>
      )}
    </SidebarProvider>
  );
}