from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import StreamingResponse
from google.cloud import storage
import logging
import os
from typing import Optional
import io

from rest_api.dependencies import get_db
from rest_api.core.firebase_auth import get_current_user, AuthUser

logger = logging.getLogger(__name__)

router = APIRouter()

# Initialize GCS client
storage_client = storage.Client()
bucket_name = os.getenv("GCS_BUCKET_NAME", "drcr-attachments")

@router.get("/{attachment_id}")
async def get_attachment(
    attachment_id: str,
    current_user: AuthUser = Depends(get_current_user),
    db=Depends(get_db)
):
    """
    Serve an attachment file from Google Cloud Storage.
    
    The attachment_id should correspond to a document in the ATTACHMENTS collection
    which contains the GCS path and metadata.
    """
    try:
        # Get attachment metadata from Firestore
        attachment_doc = await db.collection("ATTACHMENTS").document(attachment_id).get()
        
        if not attachment_doc.exists:
            raise HTTPException(status_code=404, detail="Attachment not found")
        
        attachment_data = attachment_doc.to_dict()
        
        # Get the GCS path from the attachment metadata
        gcs_path = attachment_data.get("gcs_path")
        if not gcs_path:
            raise HTTPException(status_code=404, detail="Attachment file path not found")
        
        # Get the original filename and content type
        original_filename = attachment_data.get("original_filename", "attachment")
        stored_content_type = attachment_data.get("content_type", "application/octet-stream")
        
        # Download the file from GCS
        bucket = storage_client.bucket(bucket_name)
        blob = bucket.blob(gcs_path)
        
        if not blob.exists():
            raise HTTPException(status_code=404, detail="Attachment file not found in storage")
        
        # Download the file content
        file_content = blob.download_as_bytes()
        
        # Improved content type detection
        def detect_content_type(filename: str, stored_type: str, file_content: bytes) -> str:
            # Check file extension first
            filename_lower = filename.lower()
            if filename_lower.endswith('.pdf'):
                return "application/pdf"
            elif filename_lower.endswith(('.jpg', '.jpeg')):
                return "image/jpeg"
            elif filename_lower.endswith('.png'):
                return "image/png"
            elif filename_lower.endswith('.gif'):
                return "image/gif"
            elif filename_lower.endswith('.webp'):
                return "image/webp"
            
            # Check file magic bytes for PDF
            if file_content.startswith(b'%PDF'):
                return "application/pdf"
            
            # Check for common image formats
            if file_content.startswith(b'\xff\xd8\xff'):  # JPEG
                return "image/jpeg"
            elif file_content.startswith(b'\x89PNG\r\n\x1a\n'):  # PNG
                return "image/png"
            elif file_content.startswith(b'GIF8'):  # GIF
                return "image/gif"
            elif file_content.startswith(b'RIFF') and b'WEBP' in file_content[:12]:  # WebP
                return "image/webp"
            
            # Fall back to stored content type if it's not octet-stream
            if stored_type != "application/octet-stream":
                return stored_type
            
            # Default fallback
            return "application/pdf"  # Most invoices are PDFs
        
        media_type = detect_content_type(original_filename, stored_content_type, file_content)
        
        logger.info(f"Serving attachment {attachment_id}: filename={original_filename}, detected_type={media_type}, stored_type={stored_content_type}")
        
        return StreamingResponse(
            io.BytesIO(file_content),
            media_type=media_type,
            headers={
                "Cache-Control": "public, max-age=3600",  # Cache for 1 hour
                "X-Content-Type-Options": "nosniff"  # Prevent MIME type sniffing
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error serving attachment {attachment_id}: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail="Error retrieving attachment") 