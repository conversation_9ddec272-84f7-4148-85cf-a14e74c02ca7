import React, { useState, useEffect, useMemo } from 'react';

// --- Shadcn/UI & Lucide Imports ---
import { Button } from '@/components/ui/button';
import {
    DraggableDialog,
    DraggableDialogContent,
    DraggableDialogDescription,
    DraggableDialogFooter,
    DraggableDialogHeader,
    DraggableDialogTitle,
    DraggableDialogClose,
} from '@/components/ui/draggable-dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { StatusBadge } from '@/components/ui/status-badge';
import {
    Loader2,
    Save,
    AlertCircle,
    Calendar,
    DollarSign,
    Settings,
    CheckCircle,
    XCircle,
    Info,
    Calculator,
    Users,
    FileText,
} from 'lucide-react';
import { 
    ScheduleStatus, 
    mapBackendScheduleStatus 
} from '@/types/schedule.types';

// --- Type Definitions ---
export interface BulkLineItem {
    lineItemId: string;
    scheduleId: string;
    description: string;
    lineAmount: number;
    currentAmortizationAccountCode?: string;
    currentExpenseAccountCode?: string;
    status: string;
    selected: boolean;
}

export interface BulkEditData {
    amortizationStartDate?: string;
    numberOfPeriods?: number;
    amortizationAccountCode?: string;
    expenseAccountCode?: string;
    notes?: string;
    applyToSelected: string[]; // Array of schedule IDs
    regenerateMonthlyEntries?: boolean; // Whether to regenerate monthly entries based on new config
}

// --- Component Props ---
interface BulkEditScheduleModalProps {
    isOpen: boolean;
    onClose: () => void;
    invoiceData: {
        invoiceId: string;
        reference: string;
        lineItems: BulkLineItem[];
    } | null;
    availableAmortizationAccounts: { code: string; name: string }[];
    availableExpenseAccounts: { code: string; name: string }[];
    onBulkSave: (bulkEditData: BulkEditData) => Promise<void>;
    onBulkConfirm: (scheduleIds: string[]) => Promise<void>;
    onBulkSkip: (scheduleIds: string[], reason: string) => Promise<void>;
    isSaving: boolean;
    saveError: string | null;
}

// --- Helper Functions ---
const formatDateForInput = (isoDate: string): string => {
    if (!isoDate) return '';
    try {
        return isoDate.substring(0, 10);
    } catch {
        return '';
    }
};

const calculateEndDate = (startDateIso: string, periods: number): string => {
    try {
        const startDate = new Date(startDateIso);
        const endDate = new Date(startDate.getFullYear(), startDate.getMonth() + periods, startDate.getDate() - 1);
        return endDate.toISOString();
    } catch (e) {
        console.error("Error calculating end date:", e);
        return startDateIso;
    }
};

// --- Bulk Edit Schedule Modal Component ---
export function BulkEditScheduleModal({
    isOpen,
    onClose,
    invoiceData,
    availableAmortizationAccounts = [],
    availableExpenseAccounts = [],
    onBulkSave,
    onBulkConfirm,
    onBulkSkip,
    isSaving,
    saveError,
}: BulkEditScheduleModalProps) {

    const [lineItems, setLineItems] = useState<BulkLineItem[]>([]);
    const [bulkFormData, setBulkFormData] = useState<{
        amortizationStartDate: string;
        amortizationEndDate: string;
        numberOfPeriods: number;
        amortizationAccountCode: string;
        expenseAccountCode: string;
        notes: string;
        regenerateMonthlyEntries: boolean;
    }>({
        amortizationStartDate: '',
        amortizationEndDate: '',
        numberOfPeriods: 12,
        amortizationAccountCode: '',
        expenseAccountCode: '',
        notes: '',
        regenerateMonthlyEntries: false,
    });

    const [skipReason, setSkipReason] = useState('');
    const [showSkipDialog, setShowSkipDialog] = useState(false);

    // Initialize line items when invoice data changes
    useEffect(() => {
        if (invoiceData?.lineItems) {
            setLineItems(invoiceData.lineItems.map(item => ({ ...item, selected: true })));
        } else {
            setLineItems([]);
        }
    }, [invoiceData]);

    // Calculate selected items
    const selectedItems = useMemo(() => {
        return lineItems.filter(item => item.selected);
    }, [lineItems]);

    const selectedCount = selectedItems.length;
    const totalSelectedAmount = selectedItems.reduce((sum, item) => sum + item.lineAmount, 0);

    // Handle line item selection
    const handleItemSelection = (lineItemId: string, selected: boolean) => {
        setLineItems(prev => prev.map(item => 
            item.lineItemId === lineItemId ? { ...item, selected } : item
        ));
    };

    // Handle select all / deselect all
    const handleSelectAll = (selected: boolean) => {
        setLineItems(prev => prev.map(item => ({ ...item, selected })));
    };

    // Handle bulk form field changes
    const handleBulkFieldChange = (field: string, value: any) => {
        setBulkFormData(prev => {
            const updated = { ...prev, [field]: value };
            
            // Auto-calculate end date when start date or periods change
            if (field === 'amortizationStartDate' || field === 'numberOfPeriods') {
                if (updated.amortizationStartDate && updated.numberOfPeriods > 0) {
                    updated.amortizationEndDate = calculateEndDate(updated.amortizationStartDate, updated.numberOfPeriods);
                }
            }
            
            return updated;
        });
    };

    // Handle bulk save
    const handleBulkSave = async () => {
        if (selectedCount === 0) {
            alert('Please select at least one line item to apply changes to.');
            return;
        }

        const bulkEditData: BulkEditData = {
            applyToSelected: selectedItems.map(item => item.scheduleId),
        };

        // Only include fields that have values and are not "keep existing"
        if (bulkFormData.amortizationStartDate) {
            bulkEditData.amortizationStartDate = bulkFormData.amortizationStartDate;
        }
        if (bulkFormData.numberOfPeriods > 0) {
            bulkEditData.numberOfPeriods = bulkFormData.numberOfPeriods;
        }
        if (bulkFormData.amortizationAccountCode && bulkFormData.amortizationAccountCode !== '__keep_existing__') {
            bulkEditData.amortizationAccountCode = bulkFormData.amortizationAccountCode;
        }
        if (bulkFormData.expenseAccountCode && bulkFormData.expenseAccountCode !== '__keep_existing__') {
            bulkEditData.expenseAccountCode = bulkFormData.expenseAccountCode;
        }
        if (bulkFormData.notes) {
            bulkEditData.notes = bulkFormData.notes;
        }

        try {
            await onBulkSave(bulkEditData);
        } catch (error) {
            console.error('Bulk save failed:', error);
        }
    };

    // Handle bulk confirm
    const handleBulkConfirm = async () => {
        if (selectedCount === 0) {
            alert('Please select at least one line item to confirm.');
            return;
        }

        // Check if all selected items have expense accounts
        const hasNewExpenseAccount = bulkFormData.expenseAccountCode && bulkFormData.expenseAccountCode !== '__keep_existing__';
        const itemsWithoutExpenseAccount = selectedItems.filter(item => !item.currentExpenseAccountCode && !hasNewExpenseAccount);
        if (itemsWithoutExpenseAccount.length > 0) {
            alert('All selected items must have an expense account before confirmation. Please set an expense account in the bulk edit form.');
            return;
        }

        try {
            // Save first if there are changes, then confirm
            if (Object.values(bulkFormData).some(value => value)) {
                await handleBulkSave();
            }
            await onBulkConfirm(selectedItems.map(item => item.scheduleId));
        } catch (error) {
            console.error('Bulk confirm failed:', error);
        }
    };

    // Handle bulk skip
    const handleBulkSkipSubmit = async () => {
        if (selectedCount === 0 || !skipReason.trim()) return;
        
        try {
            await onBulkSkip(selectedItems.map(item => item.scheduleId), skipReason);
            setShowSkipDialog(false);
            setSkipReason('');
        } catch (error) {
            console.error('Bulk skip failed:', error);
        }
    };



    return (
        <DraggableDialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
            <DraggableDialogContent className="flex flex-col p-0 gap-0 max-w-7xl max-h-[90vh]">
                <DraggableDialogHeader className="p-4 border-b flex-shrink-0">
                    <div className="flex items-center justify-between">
                        <div>
                            <DraggableDialogTitle className="flex items-center gap-2">
                                <Users className="h-5 w-5" />
                                Bulk Edit Amortization Schedules
                            </DraggableDialogTitle>
                            <DraggableDialogDescription>
                                <div className="flex items-center gap-2 mt-1">
                                    <FileText className="h-4 w-4" />
                                    <span>Invoice: {invoiceData?.reference}</span>
                                    <Badge variant="outline" className="text-xs">
                                        {selectedCount} of {lineItems.length} selected
                                    </Badge>
                                    {selectedCount > 0 && (
                                        <Badge variant="outline" className="text-xs bg-blue-50">
                                            Total: ${totalSelectedAmount.toFixed(2)}
                                        </Badge>
                                    )}
                                </div>
                                <div className="text-sm text-muted-foreground mt-2">
                                    Select line items below and configure bulk changes. Changes will only apply to selected items.
                                </div>
                            </DraggableDialogDescription>
                        </div>
                    </div>
                </DraggableDialogHeader>

                <div className="flex-grow grid grid-cols-1 lg:grid-cols-2 gap-0 overflow-hidden">
                    {/* Left Column: Line Item Selection */}
                    <div className="flex flex-col border-r overflow-hidden">
                        <div className="p-3 border-b bg-gray-50 flex-shrink-0">
                            <div className="flex items-center justify-between">
                                <h3 className="text-sm font-semibold flex items-center">
                                    <FileText className="h-4 w-4 mr-2" />
                                    Select Line Items ({selectedCount} selected)
                                </h3>
                                <div className="flex items-center gap-2">
                                    <Button
                                        size="sm"
                                        variant="outline"
                                        onClick={() => handleSelectAll(true)}
                                        className="text-xs"
                                    >
                                        Select All
                                    </Button>
                                    <Button
                                        size="sm"
                                        variant="outline"
                                        onClick={() => handleSelectAll(false)}
                                        className="text-xs"
                                    >
                                        Clear All
                                    </Button>
                                </div>
                            </div>
                        </div>
                        <ScrollArea className="flex-grow">
                            <div className="p-4 space-y-3">
                                {lineItems.map((item) => (
                                    <div
                                        key={item.lineItemId}
                                        className={`border rounded-lg p-3 ${item.selected ? 'border-blue-300 bg-blue-50' : 'border-gray-200'}`}
                                    >
                                        <div className="flex items-start gap-3">
                                            <Checkbox
                                                checked={item.selected}
                                                onCheckedChange={(checked) => handleItemSelection(item.lineItemId, checked as boolean)}
                                                className="mt-1"
                                            />
                                            <div className="flex-grow">
                                                <div className="flex items-center justify-between mb-2">
                                                    <span className="font-medium text-sm">{item.description}</span>
                                                    <StatusBadge status={mapBackendScheduleStatus(item.status)} size="sm" />
                                                </div>
                                                <div className="text-sm text-muted-foreground space-y-1">
                                                    <div>Amount: ${item.lineAmount.toFixed(2)}</div>
                                                    <div>Amortization Account: {item.currentAmortizationAccountCode || 'Not set'}</div>
                                                    <div>Expense Account: {item.currentExpenseAccountCode || 'Not set'}</div>
                                                    <div className="text-xs">Schedule ID: {item.scheduleId}</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                                {lineItems.length === 0 && (
                                    <div className="text-center text-muted-foreground py-8">
                                        <FileText className="h-8 w-8 mx-auto mb-2 opacity-50" />
                                        <p>No line items available</p>
                                    </div>
                                )}
                            </div>
                        </ScrollArea>
                    </div>

                    {/* Right Column: Bulk Edit Configuration */}
                    <div className="flex flex-col overflow-hidden">
                        <h3 className="text-sm font-semibold p-3 border-b bg-gray-50 flex-shrink-0 flex items-center">
                            <Settings className="h-4 w-4 mr-2" />
                            Bulk Edit Configuration
                        </h3>
                        <ScrollArea className="flex-grow p-4">
                            <div className="space-y-4">
                                {selectedCount === 0 && (
                                    <Alert>
                                        <Info className="h-4 w-4" />
                                        <AlertTitle>No Items Selected</AlertTitle>
                                        <AlertDescription>
                                            Please select at least one line item from the left panel to configure bulk changes.
                                        </AlertDescription>
                                    </Alert>
                                )}

                                {selectedCount > 0 && (
                                    <>
                                        <Alert>
                                            <Info className="h-4 w-4" />
                                            <AlertTitle>Bulk Edit Mode</AlertTitle>
                                            <AlertDescription>
                                                Changes below will be applied to {selectedCount} selected line item{selectedCount !== 1 ? 's' : ''}. 
                                                Leave fields empty to keep existing values.
                                            </AlertDescription>
                                        </Alert>

                                        {/* Schedule Configuration */}
                                        <div className="space-y-3">
                                            <h4 className="text-sm font-medium">Schedule Configuration</h4>
                                            
                                            <div className="grid grid-cols-2 gap-3">
                                                <div className="space-y-1">
                                                    <Label htmlFor="bulkStartDate">Start Date</Label>
                                                    <Input
                                                        id="bulkStartDate"
                                                        type="date"
                                                        value={formatDateForInput(bulkFormData.amortizationStartDate)}
                                                        onChange={(e) => handleBulkFieldChange('amortizationStartDate', e.target.value ? e.target.value + 'T00:00:00Z' : '')}
                                                        placeholder="Keep existing"
                                                    />
                                                </div>
                                                <div className="space-y-1">
                                                    <Label htmlFor="bulkPeriods">Number of Periods</Label>
                                                    <Input
                                                        id="bulkPeriods"
                                                        type="number"
                                                        min="1"
                                                        max="60"
                                                        value={bulkFormData.numberOfPeriods || ''}
                                                        onChange={(e) => handleBulkFieldChange('numberOfPeriods', parseInt(e.target.value, 10) || 0)}
                                                        placeholder="Keep existing"
                                                    />
                                                </div>
                                            </div>

                                            {bulkFormData.amortizationStartDate && bulkFormData.numberOfPeriods > 0 && (
                                                <div className="space-y-1">
                                                    <Label>Calculated End Date</Label>
                                                    <Input
                                                        value={formatDateForInput(bulkFormData.amortizationEndDate)}
                                                        disabled
                                                        className="bg-muted text-sm"
                                                    />
                                                </div>
                                            )}
                                        </div>

                                        {/* Account Configuration */}
                                        <div className="space-y-3 pt-4 border-t">
                                            <h4 className="text-sm font-medium">Account Configuration</h4>
                                            
                                            <div className="space-y-1">
                                                <Label htmlFor="bulkAmortizationAccount">Amortization Account (Asset)</Label>
                                                <Select
                                                    value={bulkFormData.amortizationAccountCode}
                                                    onValueChange={(value) => handleBulkFieldChange('amortizationAccountCode', value)}
                                                >
                                                    <SelectTrigger id="bulkAmortizationAccount">
                                                        <SelectValue placeholder="Keep existing accounts..." />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <SelectItem value="__keep_existing__">Keep existing accounts</SelectItem>
                                                        {availableAmortizationAccounts.map(acc => (
                                                            <SelectItem key={acc.code} value={acc.code}>
                                                                {acc.code} - {acc.name}
                                                            </SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                            </div>

                                            <div className="space-y-1">
                                                <Label htmlFor="bulkExpenseAccount">Expense Account</Label>
                                                <Select
                                                    value={bulkFormData.expenseAccountCode}
                                                    onValueChange={(value) => handleBulkFieldChange('expenseAccountCode', value)}
                                                >
                                                    <SelectTrigger id="bulkExpenseAccount">
                                                        <SelectValue placeholder="Keep existing accounts..." />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <SelectItem value="__keep_existing__">Keep existing accounts</SelectItem>
                                                        {availableExpenseAccounts.map(acc => (
                                                            <SelectItem key={acc.code} value={acc.code}>
                                                                {acc.code} - {acc.name}
                                                            </SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                            </div>
                                        </div>

                                        {/* Notes */}
                                        <div className="space-y-1 pt-4 border-t">
                                            <Label htmlFor="bulkNotes">Notes</Label>
                                            <Textarea
                                                id="bulkNotes"
                                                value={bulkFormData.notes}
                                                onChange={(e) => handleBulkFieldChange('notes', e.target.value)}
                                                placeholder="Optional notes to add to all selected schedules..."
                                                rows={3}
                                            />
                                        </div>

                                        {/* Preview */}
                                        <div className="space-y-2 pt-4 border-t">
                                            <h4 className="text-sm font-medium">Preview Changes</h4>
                                            <div className="text-xs bg-gray-50 border rounded p-3 space-y-1">
                                                <div><strong>Will apply to:</strong> {selectedCount} line item{selectedCount !== 1 ? 's' : ''}</div>
                                                <div><strong>Total amount:</strong> ${totalSelectedAmount.toFixed(2)}</div>
                                                {bulkFormData.amortizationStartDate && <div><strong>New start date:</strong> {new Date(bulkFormData.amortizationStartDate).toLocaleDateString()}</div>}
                                                {bulkFormData.numberOfPeriods > 0 && <div><strong>New periods:</strong> {bulkFormData.numberOfPeriods}</div>}
                                                {bulkFormData.amortizationAccountCode && bulkFormData.amortizationAccountCode !== '__keep_existing__' && <div><strong>New amortization account:</strong> {bulkFormData.amortizationAccountCode}</div>}
                                                {bulkFormData.expenseAccountCode && bulkFormData.expenseAccountCode !== '__keep_existing__' && <div><strong>New expense account:</strong> {bulkFormData.expenseAccountCode}</div>}
                                            </div>
                                        </div>
                                    </>
                                )}
                            </div>
                        </ScrollArea>
                    </div>
                </div>

                {saveError && (
                    <div className="px-4 pt-2 flex-shrink-0">
                        <Alert variant="destructive">
                            <AlertCircle className="h-4 w-4" />
                            <AlertTitle>Save Failed</AlertTitle>
                            <AlertDescription>{saveError}</AlertDescription>
                        </Alert>
                    </div>
                )}

                <DraggableDialogFooter className="p-4 border-t flex-shrink-0">
                    <div className="flex items-center justify-between w-full">
                        <div className="flex gap-2">
                            <Button
                                type="button"
                                variant="outline"
                                onClick={() => setShowSkipDialog(true)}
                                disabled={selectedCount === 0}
                                className="text-red-600 border-red-300 hover:bg-red-50"
                            >
                                <XCircle className="mr-2 h-4 w-4" />
                                Skip Selected ({selectedCount})
                            </Button>
                        </div>
                        <div className="flex gap-2">
                            <DraggableDialogClose asChild>
                                <Button type="button" variant="outline">
                                    Cancel
                                </Button>
                            </DraggableDialogClose>
                            <Button
                                type="button"
                                variant="outline"
                                onClick={handleBulkSave}
                                disabled={isSaving || selectedCount === 0}
                            >
                                {isSaving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                                <Save className="mr-2 h-4 w-4" />
                                Save Changes ({selectedCount})
                            </Button>
                            <Button
                                type="button"
                                onClick={handleBulkConfirm}
                                disabled={isSaving || selectedCount === 0}
                                className="bg-green-600 hover:bg-green-700"
                            >
                                {isSaving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                                <CheckCircle className="mr-2 h-4 w-4" />
                                Confirm Selected ({selectedCount})
                            </Button>
                        </div>
                    </div>
                </DraggableDialogFooter>
            </DraggableDialogContent>

            {/* Skip Dialog */}
            {showSkipDialog && (
                <DraggableDialog open={showSkipDialog} onOpenChange={setShowSkipDialog}>
                    <DraggableDialogContent>
                        <DraggableDialogHeader>
                            <DraggableDialogTitle>Skip Selected Schedules</DraggableDialogTitle>
                            <DraggableDialogDescription>
                                Please provide a reason for skipping {selectedCount} selected amortization schedule{selectedCount !== 1 ? 's' : ''}.
                            </DraggableDialogDescription>
                        </DraggableDialogHeader>
                        <div className="p-4">
                            <Textarea
                                value={skipReason}
                                onChange={(e) => setSkipReason(e.target.value)}
                                placeholder="Reason for skipping..."
                                rows={3}
                            />
                        </div>
                        <DraggableDialogFooter>
                            <Button variant="outline" onClick={() => setShowSkipDialog(false)}>
                                Cancel
                            </Button>
                            <Button
                                onClick={handleBulkSkipSubmit}
                                disabled={!skipReason.trim() || isSaving}
                                className="bg-red-600 hover:bg-red-700"
                            >
                                {isSaving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                                Skip {selectedCount} Schedule{selectedCount !== 1 ? 's' : ''}
                            </Button>
                        </DraggableDialogFooter>
                    </DraggableDialogContent>
                </DraggableDialog>
            )}
        </DraggableDialog>
    );
} 