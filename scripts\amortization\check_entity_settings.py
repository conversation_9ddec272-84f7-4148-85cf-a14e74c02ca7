#!/usr/bin/env python3
"""
Check and add materiality threshold to entity settings
"""

import asyncio
import os
import sys
from google.cloud import firestore

# Add project root to path
project_root = os.path.abspath('.')
if project_root not in sys.path:
    sys.path.insert(0, project_root)

async def check_and_update_entity_settings():
    db = firestore.AsyncClient()
    
    # Get the KSP entity settings
    entity_id = 'a8e46b01-de7d-42bb-ab01-d1a395573d51'
    entity_ref = db.collection('ENTITY_SETTINGS').document(entity_id)
    entity_doc = await entity_ref.get()
    
    if entity_doc.exists:
        settings = entity_doc.to_dict()
        print(f'Current entity settings for {entity_id}:')
        
        # Show relevant settings
        relevant_keys = [k for k in settings.keys() if 'threshold' in k.lower() or 'amortization' in k.lower()]
        if relevant_keys:
            print('Amortization-related settings:')
            for key in sorted(relevant_keys):
                print(f'  {key}: {settings[key]}')
        else:
            print('No amortization-related settings found')
        
        # Check if materiality threshold exists
        if 'amortization_materiality_threshold' in settings:
            print(f'\nMateriality threshold already exists: €{settings["amortization_materiality_threshold"]}')
        else:
            print('\nMateriality threshold NOT found in entity settings')
            
            # Add the materiality threshold
            await entity_ref.update({
                'amortization_materiality_threshold': 1000.0
            })
            print('✅ Added amortization_materiality_threshold: €1000.0 to entity settings')
            
            # Verify the update
            updated_doc = await entity_ref.get()
            updated_settings = updated_doc.to_dict()
            print(f'Verified: amortization_materiality_threshold = €{updated_settings.get("amortization_materiality_threshold", "NOT FOUND")}')
    else:
        print(f'Entity settings document {entity_id} not found')

if __name__ == "__main__":
    asyncio.run(check_and_update_entity_settings()) 