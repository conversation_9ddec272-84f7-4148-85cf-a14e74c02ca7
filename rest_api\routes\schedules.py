from fastapi import APIRouter, Depends, HTTPException, Path, Query, Body, status
from typing import List, Optional, Dict, Any
from google.cloud.firestore import SERVER_TIMESTAMP
import uuid
from datetime import datetime

from ..core.firebase_auth import get_current_user, get_firm_user_with_client_access, AuthUser
from ..dependencies import get_db
from drcr_shared_logic.clients.xero_client import XeroApiClient

router = APIRouter(tags=["Amortization Schedules"])

async def _create_audit_log_entry(
    db,
    event_category: str,
    event_type: str,
    client_id: str,
    entity_id: str,
    transaction_id: str,
    schedule_id: str,
    status: str,
    details: Dict[str, Any],
    user_id: Optional[str] = None
):
    """Creates an audit log entry in Firestore."""
    try:
        log_entry_id = str(uuid.uuid4())
        log_data = {
            "timestamp": SERVER_TIMESTAMP,
            "event_category": event_category,
            "event_type": event_type,
            "client_id": client_id,
            "entity_id": entity_id,
            "transaction_id": transaction_id,
            "schedule_id": schedule_id,
            "user_id": user_id,
            "status": status,
            "details": details
        }

        # Remove None fields
        log_data = {k: v for k, v in log_data.items() if v is not None}

        audit_log_ref = db.collection("AUDIT_LOG").document(log_entry_id)
        await audit_log_ref.set(log_data)
    except Exception as e:
        # Log error but don't fail the main operation
        print(f"Failed to create audit log: {e}")

@router.get("/{schedule_id}")
async def get_schedule(
    schedule_id: str = Path(..., description="Amortization Schedule ID"),
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db)
):
    """Get detailed information about an amortization schedule"""
    # Get schedule record
    schedule_ref = db.collection("AMORTIZATION_SCHEDULES").document(schedule_id)
    schedule_doc = await schedule_ref.get()
    
    if not schedule_doc.exists:
        raise HTTPException(status_code=404, detail="Amortization schedule not found")
    
    schedule_data = schedule_doc.to_dict()
    client_id = schedule_data.get("clientId")
    
    # Check if user has access to this client
    await get_firm_user_with_client_access(client_id, current_user)
    
    # Return schedule data
    schedule_data["schedule_id"] = schedule_id
    return schedule_data

@router.put("/{schedule_id}")
async def update_schedule(
    schedule_id: str = Path(..., description="Amortization Schedule ID"),
    schedule_data: Dict[str, Any] = Body(..., description="Updated schedule data"),
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db)
):
    """Update an amortization schedule (dates, amounts, account codes)"""
    # Get schedule record
    schedule_ref = db.collection("AMORTIZATION_SCHEDULES").document(schedule_id)
    schedule_doc = await schedule_ref.get()

    if not schedule_doc.exists:
        raise HTTPException(status_code=404, detail="Amortization schedule not found")

    existing_schedule = schedule_doc.to_dict()
    client_id = existing_schedule.get("clientId")
    entity_id = existing_schedule.get("entityId")
    transaction_id = existing_schedule.get("transactionId")
    current_status = existing_schedule.get("status")

    # Check if user has access to this client
    await get_firm_user_with_client_access(client_id, current_user)

    # Fields that can be updated
    allowed_fields = [
        "amortizationStartDate",
        "amortizationEndDate",
        "originalAmount",
        "amortizationAccountCode",
        "expenseAccountCode",
        "notes",
        "monthlyEntries"
    ]

    # Filter out fields that cannot be updated
    update_data = {k: v for k, v in schedule_data.items() if k in allowed_fields}

    if not update_data:
        raise HTTPException(status_code=400, detail="No valid fields to update")

    # Merge update data with existing schedule to check for status progression
    merged_data = {**existing_schedule, **update_data}

    # Determine if status should be automatically advanced
    new_status = _determine_automatic_status_progression(current_status, merged_data)

    # Add status update if progression occurred
    if new_status != current_status:
        update_data["status"] = new_status
        update_data[f"{new_status}_at"] = SERVER_TIMESTAMP
        update_data[f"{new_status}_by"] = current_user.uid

    # Add metadata
    update_data["updated_at"] = SERVER_TIMESTAMP
    update_data["updated_by"] = current_user.uid

    # Update the schedule
    await schedule_ref.update(update_data)

    # Create audit log entry
    audit_details = {
        "updated_fields": list(update_data.keys()),
        "user_email": current_user.email
    }

    # Add status progression info if it occurred
    if new_status != current_status:
        audit_details["status_progression"] = {
            "from": current_status,
            "to": new_status,
            "reason": "automatic_field_completion"
        }

    await _create_audit_log_entry(
        db=db,
        event_category="AMORTIZATION_MANAGEMENT",
        event_type="SCHEDULE_UPDATED",
        client_id=client_id,
        entity_id=entity_id,
        transaction_id=transaction_id,
        schedule_id=schedule_id,
        status="SUCCESS",
        details=audit_details,
        user_id=current_user.uid
    )

    response_data = {"message": "Schedule updated successfully", "schedule_id": schedule_id}

    # Include status progression info in response
    if new_status != current_status:
        response_data["status_progression"] = {
            "from": current_status,
            "to": new_status
        }

    return response_data

@router.post("/{schedule_id}/confirm")
async def confirm_schedule(
    schedule_id: str = Path(..., description="Amortization Schedule ID"),
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db)
):
    """Confirm an amortization schedule and post journals to Xero"""
    # Get schedule record
    schedule_ref = db.collection("AMORTIZATION_SCHEDULES").document(schedule_id)
    schedule_doc = await schedule_ref.get()
    
    if not schedule_doc.exists:
        raise HTTPException(status_code=404, detail="Amortization schedule not found")
    
    schedule_data = schedule_doc.to_dict()
    client_id = schedule_data.get("clientId")
    entity_id = schedule_data.get("entityId")
    transaction_id = schedule_data.get("transactionId")
    
    # Check if user has access to this client
    await get_firm_user_with_client_access(client_id, current_user)
    
    # Validate that required fields are set before confirmation
    expense_account_code = schedule_data.get("expenseAccountCode")
    amortization_account_code = schedule_data.get("amortizationAccountCode")
    
    if not expense_account_code:
        raise HTTPException(
            status_code=400,
            detail="Cannot confirm schedule: Expense account code must be set before confirmation"
        )
    
    if not amortization_account_code:
        raise HTTPException(
            status_code=400,
            detail="Cannot confirm schedule: Amortization account code must be set before confirmation"
        )
    
    # Check if schedule is in a state that can be confirmed
    current_status = schedule_data.get("status")
    if current_status not in ["pending_review", "pending_confirmation", "pending_configuration"]:
        raise HTTPException(
            status_code=400,
            detail=f"Schedule cannot be confirmed from current status: {current_status}"
        )
    
    # Update schedule status
    await schedule_ref.update({
        "status": "confirmed",
        "confirmed_at": SERVER_TIMESTAMP,
        "confirmed_by": current_user.uid,
        "updated_at": SERVER_TIMESTAMP
    })
    
    # Initialize XeroApiClient
    xero_client = await XeroApiClient.create(
        platform_org_id=entity_id,
        tenant_id=client_id
    )
    
    # Process monthly entries that need to be posted
    monthly_entries = schedule_data.get("monthlyEntries", [])
    success_count = 0
    error_count = 0
    
    for i, entry in enumerate(monthly_entries):
        entry_status = entry.get("status")
        
        # Only post entries that are due for posting
        if entry_status == "due_for_posting":
            try:
                # Prepare journal data
                journal_date = entry.get("date")
                journal_amount = entry.get("amount")
                narration = f"Amortization for {schedule_data.get('transactionReference', 'Invoice')} - {i+1}/{len(monthly_entries)}"
                
                # Create journal in Xero
                journal_result = await xero_client.create_manual_journal(
                    date=journal_date,
                    narration=narration,
                    line_items=[
                        {
                            "LineAmount": journal_amount,
                            "AccountCode": schedule_data.get("expenseAccountCode"),
                            "Description": "Expense portion"
                        },
                        {
                            "LineAmount": -journal_amount,
                            "AccountCode": schedule_data.get("assetAccountCode"),
                            "Description": "Asset reduction"
                        }
                    ]
                )
                
                # Update entry with posting details
                monthly_entries[i]["status"] = "posted"
                monthly_entries[i]["posted_at"] = datetime.now().isoformat()
                monthly_entries[i]["xero_journal_id"] = journal_result.get("ManualJournalID")
                success_count += 1
                
            except Exception as e:
                # Update entry with error details
                monthly_entries[i]["status"] = "posting_failed"
                monthly_entries[i]["error"] = str(e)
                error_count += 1
    
    # Update schedule with processed entries
    await schedule_ref.update({
        "monthlyEntries": monthly_entries,
        "updated_at": SERVER_TIMESTAMP
    })
    
    # Create audit log entry
    await _create_audit_log_entry(
        db=db,
        event_category="AMORTIZATION_MANAGEMENT",
        event_type="SCHEDULE_CONFIRMED",
        client_id=client_id,
        entity_id=entity_id,
        transaction_id=transaction_id,
        schedule_id=schedule_id,
        status="SUCCESS",
        details={
            "entries_posted": success_count,
            "entries_failed": error_count,
            "user_email": current_user.email
        },
        user_id=current_user.uid
    )
    
    return {
        "message": "Schedule confirmed successfully",
        "schedule_id": schedule_id,
        "entries_posted": success_count,
        "entries_failed": error_count
    }

@router.post("/{schedule_id}/skip")
async def skip_schedule(
    schedule_id: str = Path(..., description="Amortization Schedule ID"),
    reason: str = Body(..., embed=True, description="Reason for skipping"),
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db)
):
    """Skip an amortization schedule"""
    # Get schedule record
    schedule_ref = db.collection("AMORTIZATION_SCHEDULES").document(schedule_id)
    schedule_doc = await schedule_ref.get()
    
    if not schedule_doc.exists:
        raise HTTPException(status_code=404, detail="Amortization schedule not found")
    
    schedule_data = schedule_doc.to_dict()
    client_id = schedule_data.get("clientId")
    entity_id = schedule_data.get("entityId")
    transaction_id = schedule_data.get("transactionId")
    
    # Check if user has access to this client
    await get_firm_user_with_client_access(client_id, current_user)
    
    # Check if schedule is in a state that can be skipped
    current_status = schedule_data.get("status")
    if current_status not in ["pending_review", "pending_confirmation"]:
        raise HTTPException(
            status_code=400,
            detail=f"Schedule cannot be skipped from current status: {current_status}"
        )
    
    # Update schedule status
    await schedule_ref.update({
        "status": "skipped",
        "skipped_at": SERVER_TIMESTAMP,
        "skipped_by": current_user.uid,
        "skip_reason": reason,
        "updated_at": SERVER_TIMESTAMP
    })
    
    # Create audit log entry
    await _create_audit_log_entry(
        db=db,
        event_category="AMORTIZATION_MANAGEMENT",
        event_type="SCHEDULE_SKIPPED",
        client_id=client_id,
        entity_id=entity_id,
        transaction_id=transaction_id,
        schedule_id=schedule_id,
        status="SUCCESS",
        details={
            "reason": reason,
            "user_email": current_user.email
        },
        user_id=current_user.uid
    )
    
    return {"message": "Schedule skipped successfully", "schedule_id": schedule_id}

@router.put("/{schedule_id}/status")
async def update_schedule_status(
    schedule_id: str = Path(..., description="Amortization Schedule ID"),
    status_data: Dict[str, Any] = Body(..., description="Status update data"),
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db)
):
    """Update the status of an amortization schedule"""
    # Get schedule record
    schedule_ref = db.collection("AMORTIZATION_SCHEDULES").document(schedule_id)
    schedule_doc = await schedule_ref.get()
    
    if not schedule_doc.exists:
        raise HTTPException(status_code=404, detail="Amortization schedule not found")
    
    schedule_data = schedule_doc.to_dict()
    client_id = schedule_data.get("clientId")
    entity_id = schedule_data.get("entityId")
    transaction_id = schedule_data.get("transactionId")
    
    # Check if user has access to this client
    await get_firm_user_with_client_access(client_id, current_user)
    
    # Get new status
    new_status = status_data.get("status")
    if not new_status:
        raise HTTPException(status_code=400, detail="Status is required")
    
    # Validate status
    valid_statuses = [
        "pending_review", 
        "pending_confirmation", 
        "confirmed", 
        "skipped", 
        "cancelled"
    ]
    
    if new_status not in valid_statuses:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid status. Must be one of: {', '.join(valid_statuses)}"
        )
    
    # Update status
    update_data = {
        "status": new_status,
        f"{new_status}_at": SERVER_TIMESTAMP,
        f"{new_status}_by": current_user.uid,
        "updated_at": SERVER_TIMESTAMP
    }
    
    # Add notes if provided
    if "notes" in status_data:
        update_data["notes"] = status_data["notes"]
    
    await schedule_ref.update(update_data)
    
    # Create audit log entry
    await _create_audit_log_entry(
        db=db,
        event_category="AMORTIZATION_MANAGEMENT",
        event_type="SCHEDULE_STATUS_UPDATED",
        client_id=client_id,
        entity_id=entity_id,
        transaction_id=transaction_id,
        schedule_id=schedule_id,
        status="SUCCESS",
        details={
            "previous_status": schedule_data.get("status"),
            "new_status": new_status,
            "user_email": current_user.email
        },
        user_id=current_user.uid
    )
    
    return {"message": f"Schedule status updated to {new_status}", "schedule_id": schedule_id}


def _determine_automatic_status_progression(current_status: str, schedule_data: Dict[str, Any]) -> str:
    """
    Determine if a schedule should automatically advance to the next status based on field completion.

    Status Progression Rules:
    - PENDING_CONFIGURATION → PENDING_REVIEW: When amortizationAccountCode is set
    - PENDING_REVIEW → PENDING_CONFIRMATION: When both amortizationAccountCode AND expenseAccountCode are set
    - PENDING_CONFIRMATION → CONFIRMED: Only via explicit confirmation action (manual)

    Args:
        current_status: Current status of the schedule
        schedule_data: Complete schedule data after merging updates

    Returns:
        New status if progression should occur, otherwise current status
    """
    # Only progress from specific statuses
    if current_status not in ["pending_configuration", "pending_review"]:
        return current_status

    # Get required fields
    amortization_account = schedule_data.get("amortizationAccountCode")
    expense_account = schedule_data.get("expenseAccountCode")

    # Check for progression from PENDING_CONFIGURATION
    if current_status == "pending_configuration":
        if amortization_account:
            # If both accounts are set, skip to PENDING_CONFIRMATION
            if expense_account:
                return "pending_confirmation"
            # Otherwise, advance to PENDING_REVIEW
            return "pending_review"

    # Check for progression from PENDING_REVIEW
    elif current_status == "pending_review":
        if amortization_account and expense_account:
            return "pending_confirmation"

    # No progression needed
    return current_status
