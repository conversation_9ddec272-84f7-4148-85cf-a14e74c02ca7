#!/usr/bin/env python3
"""
Update materiality threshold to €50 and regenerate KSP schedule
"""

import asyncio
import os
import sys
from google.cloud import firestore

# Add project root to path
project_root = os.path.abspath('.')
if project_root not in sys.path:
    sys.path.insert(0, project_root)

async def update_threshold_and_regenerate():
    db = firestore.AsyncClient()
    
    entity_id = "a8e46b01-de7d-42bb-ab01-d1a395573d51"
    current_schedule_id = "f878aa6a-c89b-411e-9e6b-6d376de2dc88"
    
    print("Updating materiality threshold to €50...")
    
    # Update entity settings
    entity_ref = db.collection('ENTITY_SETTINGS').document(entity_id)
    await entity_ref.update({
        'amortization_materiality_threshold': 50.0
    })
    print("✅ Updated materiality threshold to €50.0")
    
    # Verify the update
    entity_doc = await entity_ref.get()
    entity_settings = entity_doc.to_dict()
    threshold = entity_settings.get("amortization_materiality_threshold")
    print(f"Verified threshold: €{threshold}")
    
    # Delete current schedule
    current_schedule_ref = db.collection("AMORTIZATION_SCHEDULES").document(current_schedule_id)
    current_schedule_doc = await current_schedule_ref.get()
    
    if current_schedule_doc.exists:
        await current_schedule_ref.delete()
        print(f"✅ Deleted current schedule {current_schedule_id}")
    
    # Now regenerate with new threshold
    print(f"\nKSP bill: €79.5 vs new threshold €50.0")
    print(f"Expected method: Day-based (since €79.5 >= €50.0)")
    print(f"Expected 50% rule: May 8th is day 8/31 = 25.8% through May, so should start in May")
    
    # Import and run schedule generation
    try:
        from cloud_functions.xero_sync_consumer.main import _generate_and_save_amortization_schedule
        from datetime import date
        
        # KSP details
        transaction_id = "60d8b891-aabb-4dc4-9cc9-5563eb68e8ca"
        amount = 79.5
        start_date = date(2025, 5, 8)
        end_date = date(2026, 5, 7)
        
        line_item_data = {
            "LineItemID": "52baf216-504d-4ead-abb7-b2dee1e74959",
            "Description": "One-year Premium membership",
            "LineAmount": amount,
            "AccountCode": "447"
        }
        
        parent_invoice_data = {
            "InvoiceID": transaction_id,
            "InvoiceNumber": "PRM225050000279",
            "Contact": {"Name": "KSP Rechtsanwalt"}
        }
        
        # Generate new schedule with day-based logic
        new_schedule_id = await _generate_and_save_amortization_schedule(
            db=db,
            invoice_id=transaction_id,
            line_item_data=line_item_data,
            master_period_start_date=start_date,
            master_period_end_date=end_date,
            entity_settings=entity_settings,
            client_id="49687fc2-556d-4116-b441-505771881a01",
            entity_id=entity_id,
            parent_invoice_data=parent_invoice_data,
            llm_service_start_date=start_date.isoformat(),
            llm_service_end_date=end_date.isoformat(),
            is_llm_detected=True
        )
        
        if new_schedule_id:
            print(f"✅ Generated new day-based schedule: {new_schedule_id}")
            
            # Verify the new schedule
            new_schedule_ref = db.collection("AMORTIZATION_SCHEDULES").document(new_schedule_id)
            new_schedule_doc = await new_schedule_ref.get()
            
            if new_schedule_doc.exists:
                new_data = new_schedule_doc.to_dict()
                print(f"\nDay-based schedule details:")
                print(f"  Amount: €{new_data.get('originalAmount')}")
                print(f"  Periods: {new_data.get('numberOfPeriods')}")
                print(f"  Start: {new_data.get('amortizationStartDate')}")
                print(f"  End: {new_data.get('amortizationEndDate')}")
                
                # Show all monthly entries for day-based analysis
                monthly_entries = new_data.get('monthlyEntries', [])
                print(f"\nAll {len(monthly_entries)} monthly entries (day-based distribution):")
                total_amount = 0
                for i, entry in enumerate(monthly_entries):
                    month_date = entry.get('monthDate')
                    amount = entry.get('amount')
                    total_amount += amount
                    print(f"  {i+1:2d}. {month_date} - €{amount:6.2f}")
                
                print(f"\nTotal distributed: €{total_amount:.2f}")
                print(f"Original amount:   €{new_data.get('originalAmount'):.2f}")
                
                if abs(total_amount - new_data.get('originalAmount', 0)) < 0.01:
                    print("✅ Amounts match!")
                else:
                    print("⚠️  Amount mismatch!")
                    
                # Calculate service days for verification
                total_service_days = (end_date - start_date).days + 1
                print(f"\nService period analysis:")
                print(f"  Total service days: {total_service_days}")
                print(f"  Average per day: €{new_data.get('originalAmount') / total_service_days:.4f}")
                
            else:
                print(f"❌ Could not verify new schedule {new_schedule_id}")
        else:
            print("❌ Failed to generate new schedule")
            
    except Exception as e:
        print(f"❌ Error generating schedule: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(update_threshold_and_regenerate()) 