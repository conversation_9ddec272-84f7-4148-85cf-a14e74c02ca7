# DRCR Unified Prepayment Automation Backend

This project provides a backend system for automating the processing of prepayments, initially focusing on integration with Xero. It includes features for synchronizing invoice data, intelligently scanning attached documents using AI/OCR, generating amortization schedules, and managing proposed journal entries.

The project is structured to support multiple accounting platforms in the future, with a modular architecture that separates platform-specific code from shared business logic.

### ⚠️ **CRITICAL WORKSPACE RULE**
**ALWAYS USE FULL PATHS WHEN WORKING WITH MULTIPLE PROJECTS**
- Frontend project: `/d:/Projects/drcr_front/`
- Backend project: `/d:/Projects/drcr_back/`
- When editing files, ALWAYS specify the full path to avoid working in wrong directory
- When using terminal commands, verify you're in the correct project directory
- Example: Use `/d:/Projects/drcr_front/src/components/...` NOT `src/components/...`

### 📚 **AI ASSISTANT DOCUMENTATION RULE**
**ALWAYS CHECK DOCUMENTATION BEFORE MAKING CHANGES**
- **Current Status:** Check `docs/DOCUMENTATION_INDEX.md` first
- **Project Phase:** Check `docs/dev_plan.txt` for current phase status
- **Current Tasks:** Check `docs/TASK_PLANNER_UPDATED.md` for current sprint
- **Performance Metrics:** Reference `docs/PERFORMANCE_OPTIMIZATION_SUMMARY.md`
- **Update Documentation:** Always update relevant docs when making changes

## Key Functionality

*   **Automated Xero Data Sync:**
    *   Fetches Invoices (ACCPAY), Chart of Accounts, and Contacts from Xero.
    *   **Chart of Accounts Sync:** Automatically syncs and caches chart of accounts in Firestore (`CHART_OF_ACCOUNTS` collection) for fast access.
    *   **Immediate Sync on Connection:** When a new Xero entity is connected, automatically triggers sync for Contacts and Accounts.
    *   Stores synchronized data in Firestore with a nested structure (`tenants/{tenantId}/xero_orgs/{platformOrgId}/...`).
    *   Manages last sync timestamps for incremental updates.
    *   **Cost-Effective Token Storage:** OAuth tokens are stored in encrypted Firestore documents instead of expensive Secret Manager, reducing token storage costs by 80-90%.
*   **Intelligent Invoice Processing:**
    *   Uploads invoice attachments to Google Cloud Storage.
    *   Utilizes OpenAI (e.g., GPT-4o, GPT-4.1-mini) for direct data extraction from PDF and image attachments, including:
        *   Standard invoice fields (InvoiceNumber, Date, Total, LineItems).
        *   **Enhanced Service Period Inference:** LLM attempts to infer service start/end dates, duration, and inference method/confidence from contextual keywords if explicit dates are missing.
    *   Includes a fallback mechanism using Mistral OCR for documents where direct OpenAI extraction is insufficient, followed by OpenAI text-based extraction.
    *   Validates extracted totals against Xero data.
*   **Prepayment Identification & Amortization:**
    *   Identifies prepayments based on LLM-extracted/inferred service periods (e.g., >32 days).
    *   Identifies prepayments based on GL coding (line items coded to prepayment asset accounts defined in entity settings).
    *   Derives default service periods (e.g., invoice date + 12 months) as a final fallback for GL-coded prepayments if no dates are available from LLM.
    *   Supports vendor-specific default expense accounts for GL-coded prepayments, fetched from Contact records.
    *   **Hybrid Amortization System:** Automatically selects optimal amortization method based on transaction amount:
        *   **Small amounts** (< materiality threshold): 12-month equal distribution for simplicity
        *   **Large amounts** (≥ materiality threshold): Day-based distribution with 50% rule for accuracy
        *   **Configurable threshold** per entity (default €1000) in `amortization_materiality_threshold` setting
    *   Generates detailed monthly amortization schedules for identified prepayments, stored in Firestore.
*   **Proposed Journal Management:**
    *   **Journal Generation:** Automatically creates proposed journal entries in Firestore (`PROPOSED_JOURNALS` collection) for due monthly amortization amounts.
    *   **Journal Posting to Xero (Initial Implementation):** Includes backend logic to post these proposed journals to the Xero API as Manual Journals (e.g., as Drafts). *Full end-to-end testing of this Xero posting step is currently deferred.*
*   **Chart of Accounts Management:**
    *   **Cached Access:** Chart of accounts are cached in Firestore for fast retrieval without hitting Xero API.
    *   **Automatic Sync:** Accounts are automatically synced when entities are connected and during scheduled syncs.
    *   **Fallback to Xero:** If cached data is not available, the system falls back to direct Xero API calls.
*   **Flexible Secret Management:**
    *   **Development:** Uses environment variables (`.env` file) for easy local development.
    *   **Production:** Optionally loads secrets from Google Cloud Secret Manager into environment variables at startup, providing security benefits with cost efficiency.
    *   **OAuth Tokens:** Stored encrypted in Firestore for cost-effective frequent access.

## Setup

1.  **Prerequisites:**
    *   Python 3.9+
    *   Google Cloud SDK installed and configured (`gcloud init`).
    *   Access to a GCP project with Firestore and Google Cloud Storage enabled.
    *   Access to a Xero Developer account and a Xero App set up with API credentials.
    *   OpenAI API Key.
    *   Mistral API Key.

2.  **Clone the Repository:**
    ```bash
    git clone <your-repository-url>
    cd <repository-directory>
    ```

3.  **Set up a Virtual Environment (recommended):**
    ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

4.  **Install Dependencies:**
    ```bash
   pip install -r requirements.txt
   ```
    *(Ensure you have a `requirements.txt` file with all necessary packages like `google-cloud-firestore`, `google-cloud-storage`, `python-dotenv`, `httpx`, `openai`, `Pillow`, `opencv-python`, etc.)*

5.  **Environment Variables:**
    *   Create a `.env` file in the root directory of the project.
    *   Populate it with your specific credentials and configuration:
        ```env
        # Google Cloud
        GCP_PROJECT_ID="your-gcp-project-id"
        GCP_REGION="your-gcp-region" # e.g., europe-west2
        GCS_BUCKET_NAME="your-gcs-bucket-name-for-attachments"
        FIREBASE_CREDENTIALS_PATH="path-to-firebase-credentials.json"

        # Secret Management (Production)
        LOAD_SECRETS_FROM_SECRET_MANAGER="false" # Set to "true" for production to load secrets from Secret Manager

        # Xero API (for local development - loaded from Secret Manager in production)
        XERO_CLIENT_ID="your-xero-client-id"
        XERO_CLIENT_SECRET="your-xero-client-secret"
        XERO_REDIRECT_URI="your-xero-redirect-uri" # e.g., http://localhost:8081/xero/callback
        XERO_SCOPES="accounting.transactions accounting.settings offline_access"
        XERO_TEST_TENANT_ID_FROM_ENV="your-actual-xero-tenant-id-for-testing"

        # OAuth Token Storage (Firestore)
        TOKEN_ENCRYPTION_KEY="your-fernet-encryption-key-here" # Generate using: from cryptography.fernet import Fernet; Fernet.generate_key().decode()

        # AI Services (for local development - loaded from Secret Manager in production)
        OPENAI_API_KEY="your-openai-api-key"
        OPENAI_MODEL="gpt-4.1-mini" # Or your preferred model like "gpt-4o"
        MISTRAL_API_KEY="your-mistral-api-key"
        # MISTRAL_OCR_MODEL="mistral-small-latest" # Default, can be overridden

        # API (for local development - loaded from Secret Manager in production)
        API_SECRET_KEY="your-secret-key"
        API_DEBUG=True
        API_HOST="0.0.0.0"
        API_PORT=8080

        # PubSub
        PUBSUB_TOPIC_XERO_SYNC="xero-sync-topic"
        PUBSUB_SUBSCRIPTION_XERO_SYNC="xero-sync-subscription"
        ```
    *   **Secret Management Options:**
        *   **Local Development:** Set `LOAD_SECRETS_FROM_SECRET_MANAGER=false` and provide all secrets in the `.env` file.
        *   **Production Deployment:** Set `LOAD_SECRETS_FROM_SECRET_MANAGER=true` and store sensitive secrets in Google Cloud Secret Manager. The application will load them into environment variables at startup.
        *   **Hybrid Approach:** Environment variables take precedence over Secret Manager, allowing local overrides even when Secret Manager loading is enabled.
    *   **Security Note:** For production deployments, sensitive values like `XERO_CLIENT_SECRET`, `OPENAI_API_KEY`, `MISTRAL_API_KEY`, and `TOKEN_ENCRYPTION_KEY` should be stored in Google Cloud Secret Manager. The application will automatically load these at startup when `LOAD_SECRETS_FROM_SECRET_MANAGER=true`. OAuth tokens are stored encrypted in Firestore for cost efficiency.

6.  **Google Cloud Application Default Credentials (ADC):**
    *   For local development, you'll need to authenticate for GCP services.
    ```bash
    gcloud auth application-default login
    ```

## Project Structure

The project is organized into the following main directories:

* **cloud_functions/**: Contains cloud functions that are deployed to Google Cloud Functions.
  * **xero_sync_consumer/**: The main cloud function for syncing data from Xero and processing invoices.
* **drcr_shared_logic/**: Contains shared logic used across different parts of the application.
  * **clients/**: Contains client classes for interacting with accounting platforms.
    * **base_client.py**: Abstract base class defining the interface for all accounting platform clients.
    * **xero_client.py**: Implementation of the base client for Xero.
  * **document_processor.py**: Logic for processing and extracting data from invoice documents.
* **rest_api/**: FastAPI-based REST API for the application.
* **tests/**: Organized test files and utilities.
  * **performance/**: Performance testing scripts for API endpoints and optimization validation.
  * **api/**: API integration and functional tests for various endpoints and workflows.
  * **token_management/**: OAuth token management tests and utilities for Firestore token storage.
  * **xero_integration/**: Xero-specific integration tests and manual testing tools.
  * **unit/**, **integration/**, **python/**, **powershell/**: Additional test categories.
* **scripts/**: Utility and deployment scripts.
  * **utilities/**: Development utilities (encryption key generation, configuration fixes, token generation).
  * **deployment/**: Deployment and server management scripts (fast startup, production deployment, cleanup).
  * **amortization/**: Testing and management scripts for the hybrid amortization system.
* **docs/**: Comprehensive documentation.
  * **api_guide/**: API endpoints, authentication, and router configuration.
  * **data_model/**: Firestore collections and relationships.
  * **development/**: Setup instructions, testing guidelines, and codebase structure.
  * **error_handling/**: Error codes and response formats.
  * **HYBRID_AMORTIZATION.md**: Detailed guide to the hybrid amortization system with examples and configuration.
  * **PERFORMANCE_OPTIMIZATION_SUMMARY.md**: Comprehensive performance improvements and optimization guide.
* **deployment/**: Infrastructure and deployment configurations.
* **terraform/**: Infrastructure as Code for deploying the application.
* **local_utils/**: General utility functions and helpers.

## Local Development & Testing

### Cloud Functions Testing

The `cloud_functions/xero_sync_consumer/main.py` script can be run locally for testing various scenarios.

1.  **Configuration:**
    *   Ensure your `.env` file is correctly set up (see Setup section).
    *   Ensure you have authenticated with ADC for GCP services.

2.  **Running Local Tests:**
    *   Navigate to the root of the project.
    *   Execute the main script module:
        ```bash
        python -m cloud_functions.xero_sync_consumer.main
        ```
    *   **Test Scenarios:** Inside the `if __name__ == "__main__":` block in `main.py`, you can control which test scenarios run by setting boolean flags:
        *   `run_sync_consumer_scenario`: Set to `True` to test the general Xero data synchronization for endpoints like `/Invoices`, `/Accounts`, or `/Contacts`.
            *   You can modify `invoice_sync_event_data`, `coa_sync_event_data`, or `contacts_sync_event_data` variables to target specific data or filters for the sync consumer.
        *   `run_journal_generation_scenario`: Set to `True` to test the generation of `PROPOSED_JOURNALS` from existing `AMORTIZATION_SCHEDULES` in your Firestore.
        *   `run_journal_posting_scenario`: Set to `True` to test the logic that posts `PROPOSED_JOURNALS` from Firestore to the Xero API. *(Note: While the code for API posting is present, comprehensive testing of this specific interaction with Xero is pending further review).*

3.  **Firestore Data:**
    *   For scenarios involving `AMORTIZATION_SCHEDULES` or `PROPOSED_JOURNALS`, ensure you have relevant test data populated in your local Firestore instance or the GCP project specified by `GCP_PROJECT_ID`.
    *   `ENTITY_SETTINGS` for your test `platformOrgId` (Xero Tenant ID, specified as `XERO_TEST_TENANT_ID_FROM_ENV`) should also exist in Firestore, particularly `clientId` and `prepaymentAssetAccountCodes`. The test scripts include mock data fallbacks if these are not found, but using real settings from Firestore is recommended for accurate testing.

4.  **Logging:**
    *   The script produces detailed logs to the console, which are helpful for monitoring the execution flow and diagnosing issues.

### REST API and Authentication Testing

The project includes PowerShell scripts for testing the REST API and Firebase authentication.

1. **Starting the REST API Server:**
   * Navigate to the `rest_api` directory:
     ```powershell
     cd rest_api
     python run_server.py
     ```
   * The server will start on `http://localhost:8080`

2. **Testing Authentication:**
   * The `test_firebase_auth.ps1` script tests Firebase authentication:
     ```powershell
     cd D:\Projects\drcr_back
     .\test_firebase_auth.ps1
     ```
   * This script:
     * Signs in with email and password
     * Tests the `/auth/me` endpoint
     * Registers a firm if the user doesn't have one
     * Saves the ID token to `firebase_id_token.txt`

3. **Testing API Endpoints:**
   * The `test_api.ps1` script tests various API endpoints:
     ```powershell
     .\test_api.ps1
     ```
   * This script:
     * Uses the ID token from `firebase_id_token.txt`
     * Tests the health endpoint
     * Tests the `/auth/me` endpoint
     * Tests other endpoints if the user has a firm

4. **Authentication Architecture:**
   * The system uses Firebase Authentication for user identity management
   * User data and associations are stored in Firestore
   * Role-based access control is implemented with roles like `firm_admin`
   * FastAPI middleware handles token verification and user authorization

## Deployment (Example for Cloud Functions)

(This section would typically outline steps to deploy `xero_sync_consumer` as a Google Cloud Function, including setting environment variables, specifying the trigger type (e.g., Pub/Sub), and any necessary IAM permissions.)
*Details to be added.*

## Future Development

Refer to `dev_plan.txt` for a detailed breakdown of ongoing and future development phases, including:
*   UI for managing settings and reviewing journals.
*   Comprehensive testing and CI/CD.
*   Support for other accounting platforms (the foundation for this has been laid with the modular architecture and the `BaseAccountingClient` abstract class).

### Recent Improvements
*   **Prepayment Processing Fixes (2025-01)**: Resolved critical issues with KSP prepayment processing including action button visibility, schedule data display, and entity settings loading.
*   **Schedule Data Accuracy**: Fixed frontend to display actual Firebase amortization amounts instead of uniform calculated previews.
*   **Entity Settings Enhancement**: Added support for all entity settings fields including amortization materiality threshold.
*   **Attachment Handling**: Improved attachment viewing with proper authentication and content type detection.
*   Migrated OAuth token storage from expensive Secret Manager to cost-effective encrypted Firestore storage, reducing token storage costs by 80-90%.
*   Completed the migration of shared logic to the `drcr_shared_logic` package.
*   Removed duplicate code and ensured consistent imports across the codebase.
*   Improved the architecture to better support multiple accounting platforms in the future.
*   Enhanced integration testing for FastAPI routes using dependency overrides.
*   Fixed router prefix configuration to prevent duplicate URL paths.
*   Updated documentation with best practices for FastAPI integration testing and router configuration.

### Documentation

Comprehensive documentation is available in the `docs/` directory:

* **Documentation Index**: `docs/DOCUMENTATION_INDEX.md` - Complete index of all available documentation
* **API Guide**: `docs/api_guide/` - Information about API endpoints, authentication, and router configuration
* **Data Model**: `docs/data_model/` - Details about Firestore collections and relationships (updated with current entity structure)
* **Development Guides**: `docs/development/` - Setup instructions, testing guidelines, and codebase structure
* **Error Handling**: `docs/error_handling/` - Error codes and response formats
* **Performance Optimization**: `docs/PERFORMANCE_OPTIMIZATION_SUMMARY.md` - Comprehensive performance improvements and optimization guide
* **Recent Issues & Solutions**: `docs/RECENT_ISSUES_SOLUTIONS.md` - Documentation of recent issues and their resolutions

**Recent Documentation Updates:**
* Added comprehensive issue tracking with the Xero OAuth reconnection flow resolution
* Updated data model documentation with current entity structure and connection_details
* Enhanced API documentation with data transformation examples
* Documented frontend-backend integration patterns and defensive coding practices

### Performance Optimization

The DRCR backend has been extensively optimized for performance:

* **Response Times**: Health endpoint averages 43.3ms with 340+ req/sec throughput
* **Xero Integration**: OAuth flow optimized from 30+ seconds to ~2 seconds (93% improvement)
* **Database Optimization**: Global connection pooling and batch operations
* **Compression**: GZip middleware for reduced payload sizes
* **Caching**: Intelligent response caching with appropriate TTL values
* **Monitoring**: Automatic performance tracking and slow request logging

**Performance Testing**: Use `scripts/utilities/performance_test.py` to run comprehensive performance tests:
```bash
python scripts/utilities/performance_test.py --url http://localhost:8081 --token-file firebase_id_token.txt
```

For detailed performance metrics and optimization strategies, see `docs/PERFORMANCE_OPTIMIZATION_SUMMARY.md`.
