import os
import logging
from typing import Dict, Any, Optional, List

from google.cloud import firestore
from drcr_shared_logic.clients.xero_client import XeroApiClient
from drcr_shared_logic.document_processor import (
    extract_data_with_openai_direct,
    is_direct_openai_attempt_sufficient,
    process_pdf_with_mistral_ocr,
    process_image_with_mistral_ocr,
    extract_structured_data_with_openai,
    post_process_extracted_invoice_data,
)

# Import helpers from the same package (Changed to absolute-style)
from cloud_functions.xero_sync_consumer.sync_helpers import _upload_to_gcs, get_other_secret

logger = logging.getLogger(__name__)

async def _process_attachment_with_llm_fallback(
    attachment_bytes: bytes,
    mime_type: str,
    xero_transaction_data: Dict[str, Any],
    file_name_for_logging: str = "document"
) -> Optional[Dict[str, Any]]:
    """
    Processes attachment with LLM using fallback logic:
    1. Try OpenAI direct extraction
    2. If validation fails, fall back to Mistral OCR + structured extraction
    """
    try:
        logger.info(f"Attempting OpenAI direct extraction for {file_name_for_logging}")
        openai_result = await extract_data_with_openai_direct(
            attachment_bytes, mime_type, file_name_for_logging
        )
        if openai_result:
            validated_data = await post_process_extracted_invoice_data(
                openai_result, xero_transaction_data, file_name_for_logging
            )
            if is_direct_openai_attempt_sufficient(validated_data):
                logger.info(f"OpenAI direct extraction successful for {file_name_for_logging}")
                validated_data["_system_extraction_method"] = "openai_direct"
                return validated_data
            else:
                logger.info(f"OpenAI direct extraction failed validation for {file_name_for_logging}")
        
        logger.info(f"Falling back to Mistral OCR for {file_name_for_logging}")
        mistral_api_key = get_other_secret("MISTRAL_API_KEY")
        if not mistral_api_key:
            logger.warning(f"Mistral API key not available for fallback processing of {file_name_for_logging}")
            return None
        
        if mime_type == "application/pdf":
            mistral_result = await process_pdf_with_mistral_ocr(
                attachment_bytes, mistral_api_key, file_name_for_logging
            )
        else:
            mistral_result = await process_image_with_mistral_ocr(
                attachment_bytes, mistral_api_key, file_name_for_logging
            )
        
        if mistral_result and mistral_result.get("text"):
            structured_data = await extract_structured_data_with_openai(
                mistral_result["text"], file_name_for_logging=file_name_for_logging
            )
            if structured_data:
                validated_data = await post_process_extracted_invoice_data(
                    structured_data, xero_transaction_data, file_name_for_logging
                )
                if validated_data:
                    logger.info(f"Mistral OCR + structured extraction successful for {file_name_for_logging}")
                    validated_data["_system_extraction_method"] = "mistral_ocr_structured"
                    validated_data["_system_ocr_text"] = mistral_result["text"][:1000]
                    return validated_data
        
        logger.warning(f"Both OpenAI direct and Mistral OCR failed for {file_name_for_logging}")
        return None
        
    except Exception as e_process:
        logger.error(f"Error in LLM processing for {file_name_for_logging}: {e_process}", exc_info=True)
        return None

async def _fetch_and_process_attachments(
    xero_client: XeroApiClient,
    transaction_id: str,
    transaction_data: Dict[str, Any],
    entity_settings: Dict[str, Any],
    entity_id: str,
    db: firestore.AsyncClient, # Added db client for Firestore operations
    file_name_for_logging: str = "document"
) -> List[Dict[str, Any]]:
    """
    Fetches attachments from Xero and processes them with LLM for data extraction.
    Returns list of processed attachment data.
    """
    processed_attachments = []
    
    if not entity_settings.get("enable_llm_prepayment_detection", True):
        logger.info(f"LLM prepayment detection disabled for entity {entity_id}")
        return processed_attachments
    
    try:
        document_type = transaction_data.get("Type", "")
        if document_type == "ACCPAY":
            xero_transaction_type = "Bills"
        elif document_type == "SPEND":
            xero_transaction_type = "BankTransactions"
        else:
            xero_transaction_type = "Invoices" 
        
        logger.info(f"Fetching attachments for {xero_transaction_type}/{transaction_id}")
        attachments = await xero_client.get_attachments(transaction_id, xero_transaction_type)
        
        if not attachments:
            logger.info(f"No attachments found for {xero_transaction_type}/{transaction_id}")
            return processed_attachments
        
        logger.info(f"Found {len(attachments)} attachments for {xero_transaction_type}/{transaction_id}")
        
        for attachment in attachments:
            try:
                attachment_id = attachment.get("AttachmentID")
                file_name = attachment.get("FileName", "unknown")
                mime_type = attachment.get("MimeType", "application/octet-stream")
                file_size = attachment.get("ContentLength", 0)
                
                max_size_mb = entity_settings.get("max_attachment_size_mb", 10)
                if file_size > max_size_mb * 1024 * 1024:
                    logger.warning(f"Attachment {attachment_id} ({file_name}) is too large: {file_size} bytes")
                    continue
                
                supported_types = entity_settings.get("supported_attachment_types", [
                    "application/pdf", "image/jpeg", "image/png", "image/jpg"
                ])
                if mime_type not in supported_types:
                    logger.info(f"Attachment {attachment_id} ({file_name}) type {mime_type} not supported for LLM processing")
                    continue
                
                logger.info(f"Downloading attachment {attachment_id} ({file_name})")
                attachment_bytes = await xero_client.download_attachment(transaction_id, attachment_id, xero_transaction_type)
                
                if not attachment_bytes:
                    logger.warning(f"Failed to download attachment {attachment_id}")
                    continue
                
                processed_data = await _process_attachment_with_llm_fallback(
                    attachment_bytes, mime_type, transaction_data, f"{file_name_for_logging}_{file_name}"
                )
                
                if processed_data:
                    gcs_bucket = os.getenv("GCS_BUCKET_NAME")
                    if gcs_bucket:
                        gcs_path = f"attachments/{entity_id}/{transaction_id}/{attachment_id}"
                        # Assuming _upload_to_gcs is available (imported or defined in this scope)
                        await _upload_to_gcs(gcs_bucket, gcs_path, attachment_bytes, mime_type)
                        processed_data["gcs_path"] = gcs_path
                    
                    attachment_doc = {
                        "transaction_id": transaction_id,
                        "entity_id": entity_id,
                        "source_system": "XERO",
                        "external_id": attachment_id,
                        "file_name": file_name,
                        "mime_type": mime_type,
                        "file_size": file_size,
                        "gcs_path": processed_data.get("gcs_path"),
                        "llm_processed": True,
                        "llm_extraction_data": processed_data,
                        "created_at": firestore.SERVER_TIMESTAMP,
                        "updated_at": firestore.SERVER_TIMESTAMP
                    }
                    
                    await db.collection("ATTACHMENTS").document(attachment_id).set(attachment_doc, merge=True)
                    processed_attachments.append(processed_data)
                    logger.info(f"Successfully processed attachment {attachment_id} ({file_name})")
                else:
                    logger.warning(f"Failed to process attachment {attachment_id} ({file_name}) with LLM")
                    
            except Exception as e_attachment:
                logger.error(f"Error processing attachment {attachment.get('AttachmentID', 'unknown')}: {e_attachment}", exc_info=True)
                continue
        
        logger.info(f"Successfully processed {len(processed_attachments)} attachments for {xero_transaction_type}/{transaction_id}")
        
    except Exception as e_fetch:
        logger.error(f"Error fetching attachments for transaction {transaction_id}: {e_fetch}", exc_info=True)
    
    return processed_attachments

async def _perform_combined_prepayment_analysis(
    transaction_data: Dict[str, Any],
    gl_prepayment_lines: List[Dict[str, Any]],
    processed_attachments: List[Dict[str, Any]],
    entity_settings: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Performs combined prepayment analysis using both GL coding and LLM extraction.
    Returns comprehensive prepayment analysis results.
    """
    analysis = {
        "gl_based_analysis_completed": True,
        "llm_based_analysis_completed": len(processed_attachments) > 0,
        "has_prepayment_line_items": len(gl_prepayment_lines) > 0,
        "detection_methods": [],
        "prepayment_line_items": [],
        "llm_detections": [],
        "confidence_score": 0.0,
        "recommended_action": "no_prepayment"
    }
    
    if gl_prepayment_lines:
        analysis["detection_methods"].append("GL_CODING")
        analysis["prepayment_line_items"] = [
            {
                "line_item_id": line.get("LineItemID"),
                "account_code": line.get("AccountCode"),
                "amount": line.get("LineAmount"),
                "description": line.get("Description"),
                "detection_method": "GL_CODING"
            } for line in gl_prepayment_lines
        ]
        analysis["confidence_score"] += 0.8
    
    llm_detected_prepayments = []
    best_service_period = None
    highest_confidence = 0.0
    
    for attachment_data in processed_attachments:
        if attachment_data.get("System_IsPrepayment"):
            llm_detected_prepayments.append({
                "extraction_method": attachment_data.get("_system_extraction_method"),
                "is_prepayment": attachment_data.get("System_IsPrepayment"),
                "prepayment_reason": attachment_data.get("System_PrepaymentReason"),
                "service_start_date": attachment_data.get("ExpectedServiceStartDate"),
                "service_end_date": attachment_data.get("ExpectedServiceEndDate"),
                "service_period_source": attachment_data.get("_system_servicePeriodSource"),
                "confidence": attachment_data.get("_system_servicePeriodInferenceConfidence", "unknown"),
                "total_validation": attachment_data.get("System_TotalValidation", {})
            })
            
            confidence_map = {"high": 0.9, "medium": 0.6, "low": 0.3}
            current_confidence = confidence_map.get(
                attachment_data.get("_system_servicePeriodInferenceConfidence", "low"), 0.3
            )
            
            if current_confidence > highest_confidence:
                highest_confidence = current_confidence
                best_service_period = {
                    "start_date": attachment_data.get("ExpectedServiceStartDate"),
                    "end_date": attachment_data.get("ExpectedServiceEndDate"),
                    "source": attachment_data.get("_system_servicePeriodSource"),
                    "confidence": attachment_data.get("_system_servicePeriodInferenceConfidence")
                }
    
    if llm_detected_prepayments:
        analysis["detection_methods"].append("LLM_ANALYSIS")
        analysis["llm_detections"] = llm_detected_prepayments
        analysis["confidence_score"] += highest_confidence
        analysis["best_service_period"] = best_service_period
    
    if gl_prepayment_lines and llm_detected_prepayments:
        analysis["recommended_action"] = "create_amortization_schedule"
        analysis["confidence_score"] = min(analysis["confidence_score"], 1.0)
    elif gl_prepayment_lines:
        analysis["recommended_action"] = "create_amortization_schedule"
    elif llm_detected_prepayments and highest_confidence >= 0.6:
        # Create amortization schedule for high-confidence LLM detections
        # These will be marked as "llm_detected" for review
        analysis["recommended_action"] = "create_amortization_schedule"
    else:
        analysis["recommended_action"] = "no_prepayment"
    
    return analysis 