import asyncio
from google.cloud import firestore

async def fix_ksp_attachment_id():
    db = firestore.AsyncClient()
    
    # Get the KSP transaction
    transaction_id = '60d8b891-aabb-4dc4-9cc9-5563eb68e8ca'
    
    # Find the correct attachment ID
    attachments_query = db.collection('ATTACHMENTS').where('transaction_id', '==', transaction_id)
    attachments_snapshot = await attachments_query.get()
    
    if len(attachments_snapshot) > 0:
        correct_attachment_id = attachments_snapshot[0].id
        print(f'Found correct attachment ID: {correct_attachment_id}')
        
        # Update the transaction with the correct attachment_id
        transaction_ref = db.collection('TRANSACTIONS').document(transaction_id)
        await transaction_ref.update({
            'attachment_id': correct_attachment_id
        })
        
        print(f'Updated transaction {transaction_id} with correct attachment_id: {correct_attachment_id}')
    else:
        print('No attachments found for this transaction')

if __name__ == "__main__":
    asyncio.run(fix_ksp_attachment_id()) 