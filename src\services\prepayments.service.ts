import { api } from '@/lib/api';
import type {
  PrepaymentsFilters,
  PrepaymentsResponse,
  SupplierData,
  InvoiceData,
  AmortizableLineItem,
  ScheduleData,
  MonthlyEntry,
  PaginationData
} from '@/types/schedule.types';
import {
  ScheduleStatus,
  EntryStatus,
  InvoiceStatus,
  SupplierStatus,
  mapBackendScheduleStatus,
  mapBackendEntryStatus,
  isActionNeededStatus,
  isActionableInvoiceStatus
} from '@/types/schedule.types';

// Re-export types for backward compatibility
export type {
  PrepaymentsFilters,
  PrepaymentsResponse,
  SupplierData,
  InvoiceData,
  AmortizableLineItem,
  ScheduleData,
  MonthlyEntry,
  PaginationData
};

export class PrepaymentsService {
  /**
   * Fetch prepayments dashboard data from the backend
   */
  static async getPrepaymentsData(filters: PrepaymentsFilters): Promise<PrepaymentsResponse> {
    try {
      console.log('DEBUG: Calling getDashboardTransactions with filters:', filters);

      // Quick validation
      if (!filters.client_id) {
        return {
          suppliers: [],
          pagination: { currentPage: 1, totalItems: 0, totalPages: 0 }
        };
      }

      // Use the dashboard transactions endpoint which includes schedules
      const response = await api.getDashboardTransactions({
        client_id: filters.client_id,
        entity_id: filters.entity_id,
        transaction_type: 'ACCPAY', // Focus on bills (ACCPAY) for prepayments
        require_action: filters.show_only_pending,
        page: filters.page || 1,
        limit: filters.limit || 10,
      });

      // Only log in development
      if (import.meta.env.DEV) {
        console.log('DEBUG: Dashboard API response - transactions:', response.transactions?.length || 0);
        

      }

      if (!response) {
        throw new Error('No response from API');
      }

      // For now, let's return empty data to see if the issue is in the transformation
      if (!response.transactions || response.transactions.length === 0) {
        console.log('DEBUG: No transactions found, returning empty data');
        return {
          suppliers: [],
          pagination: {
            currentPage: response.page || 1,
            pageSize: response.limit || 10,
            totalItems: response.total || 0,
            totalPages: response.total_pages || 0,
          },
        };
      }

      // Transform the backend response to match the frontend interface
      const suppliers = this.transformToSupplierData(response.transactions, filters.supplier_filter);

      return {
        suppliers,
        pagination: {
          currentPage: response.page || 1,
          pageSize: response.limit || 10,
          totalItems: response.total || 0,
          totalPages: response.total_pages || 0,
        },
      };
    } catch (error) {
      console.error('Error fetching prepayments data:', error);
      throw new Error('Failed to fetch prepayments data');
    }
  }

  /**
   * Transform backend dashboard items to supplier-grouped data
   */
  private static transformToSupplierData(dashboardItems: any[], supplierFilter?: string): SupplierData[] {
    const supplierMap = new Map<string, SupplierData>();

    if (!dashboardItems || !Array.isArray(dashboardItems)) {
      console.warn('Dashboard items is not an array:', dashboardItems);
      return [];
    }

    dashboardItems.forEach(item => {
      if (!item || !item.transaction) {
        console.warn('Invalid dashboard item:', item);
        return;
      }

      const transaction = item.transaction;
      const schedules = item.schedules || [];

      // Extract supplier information from transaction
      const supplierId = transaction.contact?.contact_id || transaction.contact_id || 'unknown';
      const supplierName = transaction.metadata?.contact_name || transaction.contact?.name || 'Unknown Supplier';

      // Skip if supplier filter is applied and doesn't match
      if (supplierFilter && !supplierName.toLowerCase().includes(supplierFilter.toLowerCase())) {
        return;
      }

      // Get or create supplier entry
      if (!supplierMap.has(supplierId)) {
        supplierMap.set(supplierId, {
          supplierId,
          supplierName,
          overallStatus: SupplierStatus.PROPOSED,
          invoices: [],
        });
      }

      const supplier = supplierMap.get(supplierId)!;

      // Transform transaction to invoice data
      const invoiceData: InvoiceData = {
        invoiceId: transaction.id,
        reference: transaction.document_number || transaction.notes || 'N/A',
        invoiceDate: transaction.date_issued,
        totalAmount: transaction.total || 0,
        currencyCode: transaction.currency || 'USD',
        hasAttachment: Boolean(transaction.has_attachments),
        attachmentId: transaction.attachment_id,
        ocrWarningMessage: transaction.ocr_warning,
        overallStatus: this.determineInvoiceStatus(schedules),
        isPartialApplication: false,
        amortizableLineItems: this.transformSchedulesToLineItems(schedules),
      };



      supplier.invoices.push(invoiceData);

      // Update supplier overall status based on invoice statuses
      supplier.overallStatus = this.determineSupplierStatus(supplier.invoices);
    });

    return Array.from(supplierMap.values());
  }

  /**
   * Transform amortization schedules to line items
   */
  private static transformSchedulesToLineItems(schedules: any[]): AmortizableLineItem[] {
    return schedules.map(schedule => {
      // Use actual monthly entries from the backend if available
      let monthlyBreakdown = this.transformMonthlyEntries(schedule.monthly_entries || []);
      
      // Only generate preview if no monthly entries exist AND it's not a pending_configuration status
      // (pending_configuration schedules should have monthly entries from the backend)
      if (Object.keys(monthlyBreakdown).length === 0 && schedule.amount && schedule.status !== 'pending_configuration') {
        const startDate = schedule.entry_date || schedule.amortizationStartDate || schedule.start_date || '2025-05-01';
        monthlyBreakdown = this.generatePreviewMonthlyBreakdown(
          schedule.amount,
          startDate,
          12 // Default to 12 months
        );
      }
      
      return {
        lineItemId: schedule.id,
        description: schedule.description || 'Amortization Schedule',
        lineAmount: schedule.amount || 0,
        scheduleId: schedule.id,
        prepaymentAccountCode: schedule.account_code || '',
        expenseAccountCode: schedule.expense_account_code || null,
        overallStatus: mapBackendScheduleStatus(schedule.status),
        monthlyBreakdown,
      };
    });
  }

  /**
   * Transform monthly entries to breakdown format
   */
  private static transformMonthlyEntries(monthlyEntries: any[]): Record<string, MonthlyEntry> {
    const breakdown: Record<string, MonthlyEntry> = {};

    monthlyEntries.forEach(entry => {
      // Handle both 'date' and 'monthDate' field names
      const dateField = entry.date || entry.monthDate;
      let monthKey = '';
      
      if (dateField) {
        try {
          // Handle different date formats
          let dateObj: Date;
          
          if (typeof dateField === 'string') {
            // Handle ISO string format
            if (dateField.includes('T') || dateField.includes('-')) {
              dateObj = new Date(dateField);
            } else {
              // Handle other string formats
              dateObj = new Date(dateField);
            }
          } else if (dateField instanceof Date) {
            dateObj = dateField;
          } else if (dateField && typeof dateField === 'object' && dateField.toDate) {
            // Handle Firebase Timestamp objects
            dateObj = dateField.toDate();
          } else {
            // Try to convert to Date
            dateObj = new Date(dateField);
          }
          
          // Extract YYYY-MM format
          if (!isNaN(dateObj.getTime())) {
            const year = dateObj.getFullYear();
            const month = String(dateObj.getMonth() + 1).padStart(2, '0');
            monthKey = `${year}-${month}`;
          }
        } catch (error) {
          console.warn('Failed to parse date field:', dateField, error);
        }
      }
      
      if (monthKey) {
        breakdown[monthKey] = {
          status: mapBackendEntryStatus(entry.status),
          amount: entry.amount || 0,
          journalId: entry.journal_id || entry.postedJournalId,
          error: entry.error_message || entry.postingError,
        };
      }
    });

    return breakdown;
  }

  /**
   * Generate preview monthly breakdown for unconfigured schedules
   */
  private static generatePreviewMonthlyBreakdown(
    totalAmount: number,
    startDate: string,
    numberOfMonths: number
  ): Record<string, MonthlyEntry> {
    const breakdown: Record<string, MonthlyEntry> = {};
    
    const monthlyAmount = totalAmount / numberOfMonths;
    const start = new Date(startDate);
    
    for (let i = 0; i < numberOfMonths; i++) {
      const currentDate = new Date(start);
      currentDate.setMonth(start.getMonth() + i);
      const monthKey = currentDate.toISOString().substring(0, 7); // YYYY-MM format
      
      breakdown[monthKey] = {
        status: EntryStatus.PROPOSED,
        amount: monthlyAmount,
      };
    }
    
    return breakdown;
  }

  /**
   * Determine invoice status based on schedules
   */
  private static determineInvoiceStatus(schedules: any[]): InvoiceStatus {
    if (!schedules.length) return InvoiceStatus.VALIDATION_ERROR;

    const hasErrors = schedules.some(s => s.status === 'validation_failed');
    if (hasErrors) return InvoiceStatus.VALIDATION_ERROR;

    const hasActionNeeded = schedules.some(s => s.status === 'pending_review' || s.status === 'pending_configuration' || s.status === 'requires_action');
    if (hasActionNeeded) return InvoiceStatus.ACTION_NEEDED;

    const allPosted = schedules.every(s => s.status === 'posted' || s.status === 'fully_posted');
    if (allPosted) return InvoiceStatus.FULLY_POSTED;

    const somePosted = schedules.some(s => s.status === 'posted' || s.status === 'partially_posted');
    if (somePosted) return InvoiceStatus.PARTIALLY_POSTED;

    const allSkipped = schedules.every(s => s.status === 'skipped');
    if (allSkipped) return InvoiceStatus.SKIPPED;

    const allConfirmed = schedules.every(s => s.status === 'confirmed');
    if (allConfirmed) return InvoiceStatus.CONFIRMED;

    return InvoiceStatus.PROPOSED;
  }

  /**
   * Determine supplier status based on invoices
   */
  private static determineSupplierStatus(invoices: InvoiceData[]): SupplierStatus {
    if (!invoices.length) return SupplierStatus.PROPOSED;

    const hasErrors = invoices.some(i => i.overallStatus === InvoiceStatus.VALIDATION_ERROR);
    if (hasErrors) return SupplierStatus.VALIDATION_ERROR;

    const hasActionNeeded = invoices.some(i => i.overallStatus === InvoiceStatus.ACTION_NEEDED || i.overallStatus === InvoiceStatus.PARTIALLY_POSTED);
    if (hasActionNeeded) return SupplierStatus.ACTION_NEEDED;

    const allPosted = invoices.every(i => i.overallStatus === InvoiceStatus.FULLY_POSTED);
    if (allPosted) return SupplierStatus.OK;

    const allConfirmed = invoices.every(i => i.overallStatus === InvoiceStatus.CONFIRMED);
    if (allConfirmed) return SupplierStatus.CONFIRMED;

    return SupplierStatus.PROPOSED;
  }



  /**
   * Confirm a schedule
   */
  static async confirmSchedule(scheduleId: string): Promise<void> {
    try {
      await api.confirmSchedule(scheduleId);
    } catch (error) {
      console.error('Error confirming schedule:', error);
      throw new Error('Failed to confirm schedule');
    }
  }

  /**
   * Skip a schedule
   */
  static async skipSchedule(scheduleId: string, reason: string): Promise<void> {
    try {
      await api.skipSchedule(scheduleId, reason);
    } catch (error) {
      console.error('Error skipping schedule:', error);
      throw new Error('Failed to skip schedule');
    }
  }

  /**
   * Update a schedule
   */
  static async updateSchedule(scheduleId: string, scheduleData: Partial<ScheduleData>): Promise<{
    message: string;
    schedule_id: string;
    status_progression?: {
      from: string;
      to: string;
    };
  }> {
    try {
      const response = await api.updateSchedule(scheduleId, scheduleData);
      return response;
    } catch (error) {
      console.error('Error updating schedule:', error);
      throw new Error('Failed to update schedule');
    }
  }

  /**
   * Get schedule details
   */
  static async getSchedule(scheduleId: string): Promise<any> {
    try {
      return await api.getSchedule(scheduleId);
    } catch (error) {
      console.error('Error fetching schedule:', error);
      throw new Error('Failed to fetch schedule details');
    }
  }
}
