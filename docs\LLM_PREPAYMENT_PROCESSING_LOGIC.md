# LLM Prepayment Processing Logic

## Overview

This document explains how the LLM-powered prepayment detection system works, including confidence scoring, GL coding requirements, and the decision matrix for automatic processing.

## Confidence Scoring System

### Confidence Assignment Rules

| Period Type | Confidence Level | Numeric Value | Description |
|-------------|------------------|---------------|-------------|
| **Explicit BillingPeriod** | `"high"` | 0.9 | LLM found clear billing period with start/end dates |
| **Explicit ServicePeriod** | `"high"` | 0.9 | LLM found clear service period with start/end dates |
| **Inferred Period (High)** | `"high"` | 0.9 | LLM inferred period with high confidence from context |
| **Inferred Period (Medium)** | `"medium"` | 0.6 | LLM inferred period with medium confidence |
| **Inferred Period (Low)** | `"low"` | 0.3 | LLM inferred period with low confidence |
| **Missing/Default** | `"low"` | 0.3 | No confidence assigned, defaults to low |

### Recent Fix (January 2025)

**Issue**: Explicit periods (BillingPeriod/ServicePeriod) were not being assigned confidence scores, defaulting to "low" (0.3).

**Fix**: Modified `drcr_shared_logic/document_processor.py` lines 563 & 571 to assign `"high"` confidence to explicit periods:

```python
processed_data["_system_servicePeriodInferenceConfidence"] = "high"  # Explicit periods get high confidence
```

## GL Coding Requirements

### Prepayment Asset Account Codes

For automatic prepayment processing, line items **must** use account codes defined in the entity's `prepayment_asset_account_codes` setting.

**Example Configuration:**
```json
{
  "prepayment_asset_account_codes": ["620", "447"]
}
```

**Common Issue**: If a bill uses account code "447" but the entity only has `["620"]` configured, the GL detection will fail even if LLM correctly identifies it as a prepayment.

## Decision Matrix for Automatic Processing

| GL Detection | LLM Detection | LLM Confidence | Action | Schedule Created | Notes |
|--------------|---------------|----------------|--------|------------------|-------|
| ✅ Yes | ✅ Yes | Any | `create_amortization_schedule` | ✅ Yes | Traditional GL + LLM confirmation |
| ✅ Yes | ❌ No | N/A | `create_amortization_schedule` | ✅ Yes | Traditional GL-based processing |
| ❌ No | ✅ Yes | ≥ 0.6 (High/Medium) | `create_amortization_schedule` | ✅ Yes | **NEW: LLM-only with high confidence** |
| ❌ No | ✅ Yes | < 0.6 (Low) | `review_for_prepayment` | ❌ No | Manual review required |
| ❌ No | ❌ No | N/A | `no_prepayment` | ❌ No | No prepayment detected |

## LLM-Only Detection Behavior

When the LLM detects a prepayment with high confidence (≥ 0.6) but there's no GL coding to prepayment asset accounts:

### Account Mapping
- **Asset Account**: Uses first account from `prepayment_asset_account_codes` (e.g., "620")
- **Expense Account**: **Left empty** - user must configure before confirmation

### Schedule Creation
LLM-detected schedules are created with:
```json
{
  "status": "pending_configuration",
  "is_llm_detected": true,
  "detection_method": "llm_only",
  "amortizationAccountCode": "620",
  "expenseAccountCode": null
}
```

### User Workflow
1. **Schedule appears in UI** with "Needs Configuration" status
2. **User sets expense account** (e.g., original account "447" or any other appropriate account)
3. **User can modify asset account** if needed (multiple prepayment accounts supported)
4. **User confirms schedule** to activate amortization processing

### API Validation
- **Update endpoint** allows modifying both `amortizationAccountCode` and `expenseAccountCode`
- **Confirm endpoint** validates that both accounts are set before allowing confirmation
- **Status transitions**: `pending_configuration` → `confirmed` (after user sets accounts)

## Processing Flow

### 1. GL Analysis
```python
prepayment_asset_codes = entity_settings.get("prepayment_asset_account_codes", [])
gl_prepayment_lines = [li for li in bill.get("LineItems", []) 
                      if li.get("AccountCode") in prepayment_asset_codes]
```

### 2. LLM Analysis
- Fetch and process attachments
- Extract service periods and confidence
- Validate against transaction totals

### 3. Combined Analysis
```python
if gl_prepayment_lines and llm_detected_prepayments:
    analysis["recommended_action"] = "create_amortization_schedule"
elif gl_prepayment_lines:
    analysis["recommended_action"] = "create_amortization_schedule"
elif llm_detected_prepayments and highest_confidence >= 0.6:
    analysis["recommended_action"] = "review_for_prepayment"
else:
    analysis["recommended_action"] = "no_prepayment"
```

### 4. Amortization Schedule Creation
Only occurs when `recommended_action == "create_amortization_schedule"`:

```python
if schedule_id: 
    processed_prepayments_count += 1
    logger.info(f"Generated amortization schedule {schedule_id} for bill {bill_id}")
```

## Troubleshooting Common Issues

### "0 prepayments processed" despite LLM detection

**Symptoms:**
- LLM correctly identifies prepayment (`is_prepayment: true`)
- Service period extracted with dates
- But sync reports "0 prepayments processed"

**Root Causes:**
1. **GL Coding Mismatch**: Account code not in `prepayment_asset_account_codes`
2. **Low Confidence**: LLM confidence < 0.6 without GL detection
3. **Missing Confidence**: Explicit periods not assigned confidence (fixed in January 2025)

**Solutions:**
1. Add account code to `prepayment_asset_account_codes`
2. Change line item account code in Xero
3. Improve LLM confidence through better document quality

### Example: KSP Rechtsanwalt Case

**Issue:**
- Account Code: "447" (not in `["620"]`)
- LLM Detection: ✅ Prepayment with high confidence
- Result: `"review_for_prepayment"` instead of auto-processing

**Fix Options:**
1. Update entity settings: `"prepayment_asset_account_codes": ["620", "447"]`
2. Change account code in Xero from "447" to "620"

## Configuration Reference

### Entity Settings
```json
{
  "enable_llm_prepayment_detection": true,
  "prepayment_asset_account_codes": ["620", "447"],
  "max_attachment_size_mb": 10,
  "supported_attachment_types": [
    "application/pdf", 
    "image/jpeg", 
    "image/png", 
    "image/jpg"
  ]
}
```

### Firestore Collections

**TRANSACTIONS**: Stores combined analysis results
```json
{
  "prepayment_analysis": {
    "recommended_action": "create_amortization_schedule",
    "confidence_score": 0.9,
    "detection_methods": ["GL_CODING", "LLM_ANALYSIS"],
    "best_service_period": {
      "start_date": "2025-05-08",
      "end_date": "2026-05-07",
      "confidence": "high"
    }
  }
}
```

**ATTACHMENTS**: Stores full LLM extraction data
```json
{
  "llm_extraction_data": {
    "System_IsPrepayment": true,
    "_system_servicePeriodInferenceConfidence": "high",
    "ExpectedServiceStartDate": "2025-05-08",
    "ExpectedServiceEndDate": "2026-05-07"
  }
}
```

## Related Files

- **Implementation**: `cloud_functions/xero_sync_consumer/llm_utils.py`
- **Document Processing**: `drcr_shared_logic/document_processor.py`
- **Main Sync Logic**: `cloud_functions/xero_sync_consumer/main.py`
- **Data Flow Guide**: `docs/LLM_DATA_FLOW_AND_STORAGE.md` 